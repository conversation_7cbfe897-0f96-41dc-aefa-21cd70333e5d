class PerformanceOptimizer {constructor() {this.metrics = {navigationStart: performance.timeOrigin,loadStart: performance.now(),domContentLoaded: null,firstPaint: null,firstContentfulPaint: null,largestContentfulPaint: null,firstInputDelay: null,cumulativeLayoutShift: 0 };this.init();}init() {this.setupCoreWebVitals();this.setupResourceMonitoring();this.setupMemoryMonitoring();this.setupNetworkOptimization();}setupCoreWebVitals() {new PerformanceObserver((entryList) => {const entries = entryList.getEntries();entries.forEach(entry => {if (entry.name === 'first-contentful-paint') {this.metrics.firstContentfulPaint = entry.startTime;console.log(`🎨 First Contentful Paint: ${Math.round(entry.startTime)}ms`);}});}).observe({entryTypes: ['paint'] });new PerformanceObserver((entryList) => {const entries = entryList.getEntries();const lastEntry = entries[entries.length - 1];this.metrics.largestContentfulPaint = lastEntry.startTime;console.log(`🖼️ Largest Contentful Paint: ${Math.round(lastEntry.startTime)}ms`);}).observe({entryTypes: ['largest-contentful-paint'] });new PerformanceObserver((entryList) => {const entries = entryList.getEntries();entries.forEach(entry => {this.metrics.firstInputDelay = entry.processingStart - entry.startTime;console.log(`⚡ First Input Delay: ${Math.round(this.metrics.firstInputDelay)}ms`);});}).observe({entryTypes: ['first-input'] });new PerformanceObserver((entryList) => {const entries = entryList.getEntries();entries.forEach(entry => {if (!entry.hadRecentInput) {this.metrics.cumulativeLayoutShift += entry.value;}});console.log(`📐 Cumulative Layout Shift: ${this.metrics.cumulativeLayoutShift.toFixed(4)}`);}).observe({entryTypes: ['layout-shift'] });}setupResourceMonitoring() {new PerformanceObserver((entryList) => {const entries = entryList.getEntries();entries.forEach(entry => {if (entry.transferSize > 100000) {console.warn(`⚠️ Large resource: ${entry.name}(${Math.round(entry.transferSize / 1024)}KB)`);}if (entry.duration > 1000) {console.warn(`⚠️ Slow resource: ${entry.name}(${Math.round(entry.duration)}ms)`);}});}).observe({entryTypes: ['resource'] });}setupMemoryMonitoring() {if ('memory' in performance) {setInterval(() => {const memory = performance.memory;const used = Math.round(memory.usedJSHeapSize / 1048576);const total = Math.round(memory.totalJSHeapSize / 1048576);if (used > 50) {console.warn(`🧠 High memory usage: ${used}MB / ${total}MB`);}},30000);}}setupNetworkOptimization() {if ('connection' in navigator) {const connection = navigator.connection;const logNetworkInfo = () => {console.log(`📶 Network: ${connection.effectiveType}(${connection.downlink}Mbps)`);if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {this.enableLowQualityMode();}};logNetworkInfo();connection.addEventListener('change',logNetworkInfo);}}enableLowQualityMode() {console.log('📱 Enabling low quality mode for slow connection');document.documentElement.classList.add('low-quality-mode');const style = document.createElement('style');style.textContent = ` .low-quality-mode .game-image img {image-rendering: pixelated;filter: blur(0.5px);}`;document.head.appendChild(style);}preloadCriticalResources(resources) {resources.forEach(resource => {const link = document.createElement('link');link.rel = 'preload';link.href = resource.url;link.as = resource.type;if (resource.crossorigin) link.crossOrigin = 'anonymous';document.head.appendChild(link);});}prefetchNextPageResources(resources) {resources.forEach(resource => {const link = document.createElement('link');link.rel = 'prefetch';link.href = resource;document.head.appendChild(link);});}optimizeImages() {const images = document.querySelectorAll('img[data-src]');images.forEach(img => {if (this.supportsWebP()) {const webpUrl = img.dataset.src.replace(/\.(jpg|jpeg|png)$/i,'.webp');img.dataset.src = webpUrl;}img.loading = 'lazy';img.decoding = 'async';});}supportsWebP() {const canvas = document.createElement('canvas');canvas.width = 1;canvas.height = 1;return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;}loadNonCriticalCSS(href) {const link = document.createElement('link');link.rel = 'preload';link.as = 'style';link.href = href;link.onload = function() {this.onload = null;this.rel = 'stylesheet';};document.head.appendChild(link);}loadNonCriticalJS(src,callback) {const script = document.createElement('script');script.src = src;script.async = true;script.defer = true;if (callback) script.onload = callback;document.head.appendChild(script);}generatePerformanceReport() {const navigation = performance.getEntriesByType('navigation')[0];const resources = performance.getEntriesByType('resource');const report = {timing: {domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,loadComplete: navigation.loadEventEnd - navigation.loadEventStart,firstContentfulPaint: this.metrics.firstContentfulPaint,largestContentfulPaint: this.metrics.largestContentfulPaint,firstInputDelay: this.metrics.firstInputDelay,cumulativeLayoutShift: this.metrics.cumulativeLayoutShift },resources: {total: resources.length,totalSize: resources.reduce((sum,r) => sum + (r.transferSize || 0),0),slowResources: resources.filter(r => r.duration > 1000).length,largeResources: resources.filter(r => (r.transferSize || 0) > 100000).length },memory: performance.memory ? {used: Math.round(performance.memory.usedJSHeapSize / 1048576),total: Math.round(performance.memory.totalJSHeapSize / 1048576) }: null };console.table(report.timing);console.table(report.resources);return report;}getOptimizationRecommendations() {const recommendations = [];if (this.metrics.firstContentfulPaint > 2000) {recommendations.push('Consider reducing critical CSS and JS bundle size');}if (this.metrics.largestContentfulPaint > 2500) {recommendations.push('Optimize largest contentful paint element (likely images)');}if (this.metrics.firstInputDelay > 100) {recommendations.push('Reduce JavaScript execution time for better interactivity');}if (this.metrics.cumulativeLayoutShift > 0.1) {recommendations.push('Add size attributes to images and reserve space for dynamic content');}return recommendations;}}class ServiceWorkerOptimizer {static async register() {if ('serviceWorker' in navigator) {try {const registration = await navigator.serviceWorker.register('/sw.js',{scope: '/' });console.log('✅ Service Worker registered');registration.addEventListener('updatefound',() => {const newWorker = registration.installing;newWorker.addEventListener('statechange',() => {if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {console.log('🔄 New version available. Refresh to update.');}});});return registration;}catch (error) {console.error('❌ Service Worker registration failed:',error);}}}}const performanceOptimizer = new PerformanceOptimizer();window.addEventListener('load',() => {performanceOptimizer.loadNonCriticalCSS('/css/main.css');ServiceWorkerOptimizer.register();setTimeout(() => {const report = performanceOptimizer.generatePerformanceReport();const recommendations = performanceOptimizer.getOptimizationRecommendations();if (recommendations.length > 0) {console.log('💡 Optimization Recommendations:');recommendations.forEach(rec => console.log(` • ${rec}`));}},5000);});window.performanceOptimizer = performanceOptimizer;