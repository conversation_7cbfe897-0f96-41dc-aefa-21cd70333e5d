<!DOCTYPE html><html amp="" lang="en" amp-version="2508201830000" class="i-amphtml-singledoc i-amphtml-standalone"><head><base href="./"><style amp-runtime="">html{overflow-x:hidden!important}html.i-amphtml-fie{height:100%!important;width:100%!important}html:not([amp4ads]),html:not([amp4ads]) body{height:auto!important}html:not([amp4ads]) body{margin:0!important}body{-webkit-text-size-adjust:100%;-moz-text-size-adjust:100%;-ms-text-size-adjust:100%;text-size-adjust:100%}html.i-amphtml-singledoc.i-amphtml-embedded{-ms-touch-action:pan-y pinch-zoom;touch-action:pan-y pinch-zoom}html.i-amphtml-fie>body,html.i-amphtml-singledoc>body{overflow:visible!important}html.i-amphtml-fie:not(.i-amphtml-inabox)>body,html.i-amphtml-singledoc:not(.i-amphtml-inabox)>body{position:relative!important}html.i-amphtml-ios-embed-legacy>body{overflow-x:hidden!important;overflow-y:auto!important;position:absolute!important}html.i-amphtml-ios-embed{overflow-y:auto!important;position:static}#i-amphtml-wrapper{overflow-x:hidden!important;overflow-y:auto!important;position:absolute!important;top:0!important;left:0!important;right:0!important;bottom:0!important;margin:0!important;display:block!important}html.i-amphtml-ios-embed.i-amphtml-ios-overscroll,html.i-amphtml-ios-embed.i-amphtml-ios-overscroll>#i-amphtml-wrapper{-webkit-overflow-scrolling:touch!important}#i-amphtml-wrapper>body{position:relative!important;border-top:1px solid transparent!important}#i-amphtml-wrapper+body{visibility:visible}#i-amphtml-wrapper+body .i-amphtml-lightbox-element,#i-amphtml-wrapper+body[i-amphtml-lightbox]{visibility:hidden}#i-amphtml-wrapper+body[i-amphtml-lightbox] .i-amphtml-lightbox-element{visibility:visible}#i-amphtml-wrapper.i-amphtml-scroll-disabled,.i-amphtml-scroll-disabled{overflow-x:hidden!important;overflow-y:hidden!important}amp-instagram{padding:54px 0px 0px!important;background-color:#fff}amp-iframe iframe{box-sizing:border-box!important}[amp-access][amp-access-hide]{display:none}[subscriptions-dialog],body:not(.i-amphtml-subs-ready) [subscriptions-action],body:not(.i-amphtml-subs-ready) [subscriptions-section]{display:none!important}amp-experiment,amp-live-list>[update]{display:none}amp-list[resizable-children]>.i-amphtml-loading-container.amp-hidden{display:none!important}amp-list [fetch-error],amp-list[load-more] [load-more-button],amp-list[load-more] [load-more-end],amp-list[load-more] [load-more-failed],amp-list[load-more] [load-more-loading]{display:none}amp-list[diffable] div[role=list]{display:block}amp-story-page,amp-story[standalone]{min-height:1px!important;display:block!important;height:100%!important;margin:0!important;padding:0!important;overflow:hidden!important;width:100%!important}amp-story[standalone]{background-color:#000!important;position:relative!important}amp-story-page{background-color:#757575}amp-story .amp-active>div,amp-story .i-amphtml-loader-background{display:none!important}amp-story-page:not(:first-of-type):not([distance]):not([active]){transform:translateY(1000vh)!important}amp-autocomplete{position:relative!important;display:inline-block!important}amp-autocomplete>input,amp-autocomplete>textarea{padding:0.5rem;border:1px solid rgba(0,0,0,.33)}.i-amphtml-autocomplete-results,amp-autocomplete>input,amp-autocomplete>textarea{font-size:1rem;line-height:1.5rem}[amp-fx^=fly-in]{visibility:hidden}amp-script[nodom],amp-script[sandboxed]{position:fixed!important;top:0!important;width:1px!important;height:1px!important;overflow:hidden!important;visibility:hidden}
/*# sourceURL=/css/ampdoc.css*/[hidden]{display:none!important}.i-amphtml-element{display:inline-block}.i-amphtml-blurry-placeholder{transition:opacity 0.3s cubic-bezier(0.0,0.0,0.2,1)!important;pointer-events:none}[layout=nodisplay]:not(.i-amphtml-element){display:none!important}.i-amphtml-layout-fixed,[layout=fixed][width][height]:not(.i-amphtml-layout-fixed){display:inline-block;position:relative}.i-amphtml-layout-responsive,[layout=responsive][width][height]:not(.i-amphtml-layout-responsive),[width][height][heights]:not([layout]):not(.i-amphtml-layout-responsive),[width][height][sizes]:not(img):not([layout]):not(.i-amphtml-layout-responsive){display:block;position:relative}.i-amphtml-layout-intrinsic,[layout=intrinsic][width][height]:not(.i-amphtml-layout-intrinsic){display:inline-block;position:relative;max-width:100%}.i-amphtml-layout-intrinsic .i-amphtml-sizer{max-width:100%}.i-amphtml-intrinsic-sizer{max-width:100%;display:block!important}.i-amphtml-layout-container,.i-amphtml-layout-fixed-height,[layout=container],[layout=fixed-height][height]:not(.i-amphtml-layout-fixed-height){display:block;position:relative}.i-amphtml-layout-fill,.i-amphtml-layout-fill.i-amphtml-notbuilt,[layout=fill]:not(.i-amphtml-layout-fill),body noscript>*{display:block;overflow:hidden!important;position:absolute;top:0;left:0;bottom:0;right:0}body noscript>*{position:absolute!important;width:100%;height:100%;z-index:2}body noscript{display:inline!important}.i-amphtml-layout-flex-item,[layout=flex-item]:not(.i-amphtml-layout-flex-item){display:block;position:relative;-ms-flex:1 1 auto;flex:1 1 auto}.i-amphtml-layout-fluid{position:relative}.i-amphtml-layout-size-defined{overflow:hidden!important}.i-amphtml-layout-awaiting-size{position:absolute!important;top:auto!important;bottom:auto!important}i-amphtml-sizer{display:block!important}@supports (aspect-ratio:1/1){i-amphtml-sizer.i-amphtml-disable-ar{display:none!important}}.i-amphtml-blurry-placeholder,.i-amphtml-fill-content{display:block;height:0;max-height:100%;max-width:100%;min-height:100%;min-width:100%;width:0;margin:auto}.i-amphtml-layout-size-defined .i-amphtml-fill-content{position:absolute;top:0;left:0;bottom:0;right:0}.i-amphtml-replaced-content,.i-amphtml-screen-reader{padding:0!important;border:none!important}.i-amphtml-screen-reader{position:fixed!important;top:0px!important;left:0px!important;width:4px!important;height:4px!important;opacity:0!important;overflow:hidden!important;margin:0!important;display:block!important;visibility:visible!important}.i-amphtml-screen-reader~.i-amphtml-screen-reader{left:8px!important}.i-amphtml-screen-reader~.i-amphtml-screen-reader~.i-amphtml-screen-reader{left:12px!important}.i-amphtml-screen-reader~.i-amphtml-screen-reader~.i-amphtml-screen-reader~.i-amphtml-screen-reader{left:16px!important}.i-amphtml-unresolved{position:relative;overflow:hidden!important}.i-amphtml-select-disabled{-webkit-user-select:none!important;-ms-user-select:none!important;user-select:none!important}.i-amphtml-notbuilt,[layout]:not(.i-amphtml-element),[width][height][heights]:not([layout]):not(.i-amphtml-element),[width][height][sizes]:not(img):not([layout]):not(.i-amphtml-element){position:relative;overflow:hidden!important;color:transparent!important}.i-amphtml-notbuilt:not(.i-amphtml-layout-container)>*,[layout]:not([layout=container]):not(.i-amphtml-element)>*,[width][height][heights]:not([layout]):not(.i-amphtml-element)>*,[width][height][sizes]:not([layout]):not(.i-amphtml-element)>*{display:none}amp-img:not(.i-amphtml-element)[i-amphtml-ssr]>img.i-amphtml-fill-content{display:block}.i-amphtml-notbuilt:not(.i-amphtml-layout-container),[layout]:not([layout=container]):not(.i-amphtml-element),[width][height][heights]:not([layout]):not(.i-amphtml-element),[width][height][sizes]:not(img):not([layout]):not(.i-amphtml-element){color:transparent!important;line-height:0!important}.i-amphtml-ghost{visibility:hidden!important}.i-amphtml-element>[placeholder],[layout]:not(.i-amphtml-element)>[placeholder],[width][height][heights]:not([layout]):not(.i-amphtml-element)>[placeholder],[width][height][sizes]:not([layout]):not(.i-amphtml-element)>[placeholder]{display:block;line-height:normal}.i-amphtml-element>[placeholder].amp-hidden,.i-amphtml-element>[placeholder].hidden{visibility:hidden}.i-amphtml-element:not(.amp-notsupported)>[fallback],.i-amphtml-layout-container>[placeholder].amp-hidden,.i-amphtml-layout-container>[placeholder].hidden{display:none}.i-amphtml-layout-size-defined>[fallback],.i-amphtml-layout-size-defined>[placeholder]{position:absolute!important;top:0!important;left:0!important;right:0!important;bottom:0!important;z-index:1}amp-img[i-amphtml-ssr]:not(.i-amphtml-element)>[placeholder]{z-index:auto}.i-amphtml-notbuilt>[placeholder]{display:block!important}.i-amphtml-hidden-by-media-query{display:none!important}.i-amphtml-element-error{background:red!important;color:#fff!important;position:relative!important}.i-amphtml-element-error:before{content:attr(error-message)}i-amp-scroll-container,i-amphtml-scroll-container{position:absolute;top:0;left:0;right:0;bottom:0;display:block}i-amp-scroll-container.amp-active,i-amphtml-scroll-container.amp-active{overflow:auto;-webkit-overflow-scrolling:touch}.i-amphtml-loading-container{display:block!important;pointer-events:none;z-index:1}.i-amphtml-notbuilt>.i-amphtml-loading-container{display:block!important}.i-amphtml-loading-container.amp-hidden{visibility:hidden}.i-amphtml-element>[overflow]{cursor:pointer;position:relative;z-index:2;visibility:hidden;display:initial;line-height:normal}.i-amphtml-layout-size-defined>[overflow]{position:absolute}.i-amphtml-element>[overflow].amp-visible{visibility:visible}template{display:none!important}.amp-border-box,.amp-border-box *,.amp-border-box :after,.amp-border-box :before{box-sizing:border-box}amp-pixel{display:none!important}amp-analytics,amp-auto-ads,amp-story-auto-ads{position:fixed!important;top:0!important;width:1px!important;height:1px!important;overflow:hidden!important;visibility:hidden}amp-story{visibility:hidden!important}html.i-amphtml-fie>amp-analytics{position:initial!important}[visible-when-invalid]:not(.visible),form [submit-error],form [submit-success],form [submitting]{display:none}amp-accordion{display:block!important}@media (min-width:1px){:where(amp-accordion>section)>:first-child{margin:0;background-color:#efefef;padding-right:20px;border:1px solid #dfdfdf}:where(amp-accordion>section)>:last-child{margin:0}}amp-accordion>section{float:none!important}amp-accordion>section>*{float:none!important;display:block!important;overflow:hidden!important;position:relative!important}amp-accordion,amp-accordion>section{margin:0}amp-accordion:not(.i-amphtml-built)>section>:last-child{display:none!important}amp-accordion:not(.i-amphtml-built)>section[expanded]>:last-child{display:block!important}
/*# sourceURL=/css/ampshared.css*/</style><style amp-extension="amp-loader">.i-amphtml-loader-background{position:absolute;top:0;left:0;bottom:0;right:0;background-color:#f8f8f8}.i-amphtml-new-loader{display:inline-block;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:0;height:0;color:#aaa}.i-amphtml-new-loader-size-default,.i-amphtml-new-loader-size-small{width:72px;height:72px}.i-amphtml-new-loader-logo{transform-origin:center;opacity:0;animation:i-amphtml-new-loader-scale-and-fade-in 0.8s ease-in forwards;animation-delay:0.6s;animation-delay:calc(0.6s - var(--loader-delay-offset))}.i-amphtml-new-loader-size-small .i-amphtml-new-loader-logo{display:none}.i-amphtml-new-loader-logo-default{fill:currentColor;animation:i-amphtml-new-loader-fade-out 0.8s ease-out forwards;animation-delay:1.8s;animation-delay:calc(1.8s - var(--loader-delay-offset))}.i-amphtml-new-loader-has-shim{color:#fff!important}.i-amphtml-new-loader-shim{width:72px;height:72px;border-radius:50%;display:none;transform-origin:center;opacity:0;background-color:rgba(0,0,0,.6);animation:i-amphtml-new-loader-scale-and-fade-in 0.8s ease-in forwards;animation-delay:0.6s;animation-delay:calc(0.6s - var(--loader-delay-offset))}.i-amphtml-new-loader-size-small .i-amphtml-new-loader-shim{width:48px;height:48px;margin:12px}.i-amphtml-new-loader-has-shim .i-amphtml-new-loader-shim{display:initial}.i-amphtml-new-loader-has-shim .i-amphtml-new-loader-logo-default{display:none}.i-amphtml-new-loader-has-shim .i-amphtml-new-loader-transparent-on-shim{fill:transparent!important}.i-amphtml-new-loader-logo,.i-amphtml-new-loader-shim,.i-amphtml-new-loader-spinner-wrapper{position:absolute;top:0;left:0;bottom:0;right:0}.i-amphtml-new-loader-spinner-wrapper{margin:12px}.i-amphtml-new-loader-spinner{stroke:currentColor;stroke-width:1.5px;opacity:0;animation:i-amphtml-new-loader-fade-in 0.8s ease-in forwards;animation-delay:1.8s;animation-delay:calc(1.8s - var(--loader-delay-offset))}.i-amphtml-new-loader-spinner-path{animation:frame-position-first-spin 0.6s steps(30),frame-position-infinite-spin 1.2s steps(59) infinite;animation-delay:2.8s,3.4s;animation-delay:calc(2.8s - var(--loader-delay-offset)),calc(3.4s - var(--loader-delay-offset))}.i-amphtml-new-loader-size-small .i-amphtml-new-loader-spinner{transform:scale(0.54545);stroke-width:2.75px}.i-amphtml-new-loader-size-small .i-amphtml-new-loader-spinner-path{animation-delay:1.4s,2s;animation-delay:calc(1.4s - var(--loader-delay-offset)),calc(2s - var(--loader-delay-offset))}.i-amphtml-new-loader *{animation-play-state:paused}.amp-active>.i-amphtml-new-loader *{animation-play-state:running}.i-amphtml-new-loader-ad-logo{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.i-amphtml-new-loader-ad-label{all:initial!important;display:inline-block!important;padding:0 0.4ch!important;border:1px solid!important;border-radius:2px!important;color:currentColor!important;font-size:11px!important;font-family:sans-serif!important;line-height:1.1!important;visibility:inherit!important}@keyframes i-amphtml-new-loader-fade-in{0%{opacity:0}to{opacity:1}}@keyframes i-amphtml-new-loader-fade-out{0%{opacity:1}to{opacity:0}}@keyframes i-amphtml-new-loader-scale-and-fade-in{0%{opacity:0;transform:scale(0)}50%{transform:scale(1)}to{opacity:1}}@keyframes frame-position-first-spin{0%{transform:translateX(0)}to{transform:translateX(-1440px)}}@keyframes frame-position-infinite-spin{0%{transform:translateX(-1440px)}to{transform:translateX(-4272px)}}
/*# sourceURL=/extensions/amp-loader/0.1/amp-loader.css*/</style><style amp-extension="amp-sidebar">amp-sidebar{--story-page-vh:1vh;position:fixed!important;top:0;max-height:100vh!important;height:100vh;max-width:80vw;background-color:#efefef;min-width:45px!important;outline:none;overflow-x:hidden!important;overflow-y:auto!important;z-index:2147483647;-webkit-overflow-scrolling:touch;will-change:transform}amp-sidebar[side=left]{left:0!important;transform:translateX(-100%);animation-name:i-amphtml-sidebar-slide-out-left}amp-sidebar[side=left][open]{animation-name:i-amphtml-sidebar-slide-in-left}amp-sidebar[side=right]{right:0!important;transform:translateX(100%);animation-name:i-amphtml-sidebar-slide-out-right}amp-sidebar[side=right][open]{animation-name:i-amphtml-sidebar-slide-in-right}amp-sidebar[side][i-amphtml-sidebar-opened]{transform:none;animation:none}.i-amphtml-sidebar-mask,amp-sidebar[side]{animation-duration:233ms;animation-timing-function:cubic-bezier(0,0,.21,1);animation-fill-mode:forwards}.i-amphtml-toolbar>ol,.i-amphtml-toolbar>ul{display:-ms-flexbox;display:flex;-ms-flex-pack:start;justify-content:flex-start;-ms-flex-wrap:wrap;flex-wrap:wrap;overflow:auto;list-style-type:none;padding:0;margin:0}.amp-sidebar-mask{background-color:rgba(0,0,0,.5)}.i-amphtml-sidebar-mask{position:fixed!important;top:0!important;left:0!important;width:100vw!important;height:100vh!important;background-image:none!important;animation-name:i-amphtml-sidebar-mask-fade-out;z-index:2147483646}.i-amphtml-sidebar-mask[open]{animation-name:i-amphtml-sidebar-mask-fade-in}.i-amphtml-sidebar-mask[i-amphtml-sidebar-opened]{animation:none}@keyframes i-amphtml-sidebar-slide-in-left{0%{transform:translateX(-100%)}to{transform:translateX(0)}}@keyframes i-amphtml-sidebar-slide-out-left{0%{transform:translateX(0)}to{transform:translateX(-100%)}}@keyframes i-amphtml-sidebar-slide-in-right{0%{transform:translateX(100%)}to{transform:translateX(0)}}@keyframes i-amphtml-sidebar-slide-out-right{0%{transform:translateX(0)}to{transform:translateX(100%)}}@keyframes i-amphtml-sidebar-mask-fade-in{0%{opacity:0}to{opacity:1}}@keyframes i-amphtml-sidebar-mask-fade-out{0%{opacity:1}to{opacity:0}}
/*# sourceURL=/extensions/amp-sidebar/0.1/amp-sidebar.css*/</style><style amp-extension="amp-list">amp-list.i-amphtml-list-fetch-error [fetch-error],amp-list[load-more] [load-more-button].amp-visible,amp-list[load-more] [load-more-end].amp-visible,amp-list[load-more] [load-more-failed].amp-visible,amp-list[load-more] [load-more-loading].amp-visible{display:block}amp-list[load-more] [load-more-button].i-amphtml-default-ui,amp-list[load-more] [load-more-failed].i-amphtml-default-ui,amp-list[load-more] [load-more-loading].i-amphtml-default-ui{height:80px;padding:12px 0px;box-sizing:border-box}.i-amphtml-list-load-more-button,amp-list[load-more] [load-more-button].i-amphtml-default-ui,amp-list[load-more] [load-more-failed].i-amphtml-default-ui,amp-list[load-more] [load-more-loading].i-amphtml-default-ui{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;font-weight:700;font-size:14px;text-transform:uppercase;letter-spacing:.1em;color:#333;text-align:center}amp-list[load-more] [load-more-loading].i-amphtml-default-ui .i-amphtml-list-load-more-spinner{display:inline-block;width:40px;height:40px;margin:8px 0px;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><defs><linearGradient id="a"><stop stop-color="%23333" stop-opacity=".75"/><stop offset="100%" stop-color="%23333" stop-opacity="0"/></linearGradient></defs><path fill="none" stroke="url(%23a)" stroke-width="1.725" d="M11 4.4A18 18 0 1 0 38 20"/></svg>');animation:amp-list-load-more-spinner 1000ms linear infinite}@keyframes amp-list-load-more-spinner{0%{transform:rotate(0deg)}to{transform:rotate(360deg)}}.i-amphtml-list-load-more-button{border:none;display:inline-block;background-color:rgba(51,51,51,.75);color:#fff;margin:4px 0px;padding:0px 32px;box-sizing:border-box;height:48px;border-radius:24px}[load-more] div[role=list]{overflow-y:hidden}.i-amphtml-list-load-more-button,.i-amphtml-list-load-more-button label,.i-amphtml-list-load-more-icon{cursor:pointer}.i-amphtml-list-load-more-button:hover{background-color:#333}.i-amphtml-list-load-more-button.i-amphtml-list-load-more-button-small{margin:0px;padding:4px 16px;height:32px}.i-amphtml-list-load-more-button label{display:inline-block;vertical-align:middle;line-height:24px}amp-list[load-more] [load-more-failed].i-amphtml-default-ui .i-amphtml-list-load-more-message{line-height:24px}amp-list[load-more] [load-more-failed].i-amphtml-default-ui .i-amphtml-list-load-more-icon{height:24px;width:24px;display:inline-block;vertical-align:middle;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path fill="%23fff" d="M17.65 6.35A7.96 7.96 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"/></svg>')}
/*# sourceURL=/extensions/amp-list/0.1/amp-list.css*/</style><style amp-extension="amp-form">form.amp-form-submit-error [submit-error],form.amp-form-submit-success [submit-success],form.amp-form-submitting [submitting]{display:block}textarea[autoexpand]:not(.i-amphtml-textarea-max){overflow:hidden!important}.i-amphtml-textarea-clone{visibility:hidden;position:absolute;top:-9999px;left:-9999px;height:0!important}.i-amphtml-validation-bubble{transform:translate(-50%,-100%);background-color:#fff;box-shadow:0 5px 15px 0 rgba(0,0,0,.5);max-width:200px;position:absolute;display:block;box-sizing:border-box;padding:10px;border-radius:5px}.i-amphtml-validation-bubble:after{content:" ";position:absolute;bottom:-8px;left:30px;width:0;height:0;border-left:8px solid transparent;border-right:8px solid transparent;border-top:8px solid #fff}[visible-when-invalid]{color:red}
/*# sourceURL=/extensions/amp-form/0.1/amp-form.css*/</style>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title itemprop="PLAYEntityOfPage">RTP Gengtoto Paling Gacor</title>
    
    <meta name="googlebot" content="noindex">
    <script async="" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0.js"></script>
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" type="image/png" href="assets/favicon.png.03f868b8e3.png">
    <script async="" custom-element="amp-install-serviceworker" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0/amp-install-serviceworker-0.1.js"></script>
    <script async="" custom-element="amp-form" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
    <script async="" custom-template="amp-mustache" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0/amp-mustache-0.2.js"></script>
    <script async="" custom-element="amp-list" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0/amp-list-0.1.js"></script>
    <script async="" custom-element="amp-bind" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
    <script async="" custom-element="amp-sidebar" type="text/plain" data-src-preserved="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"></script>
    <link rel="stylesheet" href="assets/css2.5af26c8529.48">

    <link rel="canonical" href="http://localhost">

    <style amp-boilerplate="">
        body {
            -webkit-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -moz-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -ms-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            animation: -amp-start 8s steps(1, end) 0s 1 normal both
        }

        @-webkit-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-moz-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-ms-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-o-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @keyframes  -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }
    </style>
    <noscript>
        <style amp-boilerplate="">
            body {
                -webkit-animation: none;
                -moz-animation: none;
                -ms-animation: none;
                animation: none
            }
        </style>
    </noscript>
    <meta name="description" content="RTP Gengtoto Paling Gacor">
    <link href="https://static.hokibagus.club/gengtoto/rtpslot/gengtoto_fav_icon.png" rel="shortcut icon" sizes="16x16">
    <style amp-custom="">
        .btn {
            display: inline-block;
            padding: 6px 12px;
            touch-action: manipulation;
            cursor: pointer;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 5px;
            font: 500 18.5px BebasNeue;
            width: 100%;
            color: #FAF0D7;
            text-shadow: 0 0 3px #000;
            letter-spacing: 1px
        }

        .btn-one {
            transition: all 0.3s;
            position: relative;
        }

        .btn-one span {
            transition: all 0.3s;
        }

        .btn-one::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0;
            transition: all 0.3s;
            border-top-width: 1.8px;
            border-bottom-width: 1.8px;
            border-top-style: solid;
            border-bottom-style: solid;
            border-top-color: rgba(250, 240, 215, 0.5);
            border-bottom-color: rgba(250, 240, 215, 0.5);
            transform: scale(0.1, 1);
        }

        .btn-one:hover span {
            letter-spacing: 2px;
        }

        .btn-one:hover::before {
            opacity: 1;
            transform: scale(1, 1);
        }

        .btn-one::after {
            border-radius: 5px;
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            transition: all 0.3s;
            background-color: rgba(250, 240, 215, 0.4);
        }

        .btn-one:hover::after {
            opacity: 0;
            transform: scale(0.1, 1);
        }




        .material-symbols-rounded {
            font-size: 15px;
            font-variation-settings:
                'FILL' 1,
                'wght' 800,
                'GRAD' 600,
                'opsz' 30
        }


        .title-prediction {
            color: #fff;
            font-weight: 700;
            text-align: center;
            font-size: 22px;
            margin-top: 17px;
            text-decoration: underline
        }

        .content-payment {
            margin: 7px;
            text-align: center;
            justify-content: center;
            align-items: center;
        }

        .items-image {
            text-align: center;
            justify-content: center;
            align-items: center;
        }


        .paraf-content {
            font-size: 18px;
            text-align: center;
            margin: 5px;
            color: white;
        }

        .paraf-content-under {
            font-size: 18px;
            text-align: center;
            margin: 5px;
            color: white;
            margin-bottom: 25px;
        }

        .title-payment {
            color: white;
            font-weight: 700;
            text-align: center;
            font-size: 25px;
            margin: 5px;
            margin-top: 25px;
        }

        .table-responsive {
            min-height: .01%;
            overflow-x: auto
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0
        }

        .table td,
        .table th {
            background-color: #F3F5EF;
            border: 1px solid #bbb;
            color: #333;
            font-family: sans-serif;
            font-size: 85%;
            padding: 10px;
            vertical-align: top
        }

        .table tr:nth-child(even) td {
            background-color: #F0F0E5
        }

        .table th {
            background-color: #EAE2CF;
            color: #333;
            font-size: 95%
        }

        .table tr.even:hover td,
        .table tr:hover td {
            color: #222;
            background-color: #FFFBEF
        }

        .tg-bf {
            font-weight: 700
        }

        .tg-it {
            font-style: italic
        }

        .tg-left {
            text-align: left
        }

        .tg-right {
            text-align: right
        }

        .tg-center {
            text-align: center
        }

        @media  screen and (max-width:767px) {
            .table-responsive {
                width: 100%;
                margin-bottom: 15px;
                overflow-y: hidden;
                -ms-overflow-style: -ms-autohiding-scrollbar
            }

            .table-responsive>.table {
                margin-bottom: 0
            }

            .table-responsive>.table>tbody>tr>td,
            .table-responsive>.table>tbody>tr>th,
            .table-responsive>.table>tfoot>tr>td,
            .table-responsive>.table>tfoot>tr>th,
            .table-responsive>.table>thead>tr>td,
            .table-responsive>.table>thead>tr>th {
                white-space: nowrap
            }
        }

        .table-layout {
            margin-top: 20px;
            margin-bottom: 25px
        }

        .table-title {
            background-color: #E7CEA6;
            padding: 5px
        }

        .table-session {
            background-color: #F4F2DE;
            padding: 5px
        }

        .author {
            text-align: right;
            font-size: 15px;
            margin-bottom: 25px;
            margin-top: 25px;
            color: white;
        }


        .fixed-height-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 20px 0px;
        }

        amp-img.contain img {
            object-fit: contain;
        }

        .items-payment {
            margin: 20px;

        }

        .menu-side {
            color: #FAF0D7;
            font-size: 14px;
            font-weight: 700;
            text-align: center;
            padding: 12px;
            border-bottom: solid #FAF0D7 1px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            text-transform: uppercase
        }

        .menu-side-type {
            color: #FAF0D7;
            font-size: 14px;
            font-weight: 700;
            text-align: left;
            padding: 5px;
            border-bottom: solid #FAF0D7 1px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            text-transform: uppercase
        }

        .side-prediksi {
            color: #FAF0D7;
            font-size: 16px;
            font-weight: 700;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
        }

        .show-more {
            display: none;
        }

        .title-page {
            color: whitesmoke;
            background: rgb(0, 0, 0);
            background: -moz-linear-gradient(0deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.50) 100%);
            background: -webkit-linear-gradient(0deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.50) 100%);
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.50) 100%);
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#000000", endColorstr="#000000", GradientType=1);
            border-radius: 3px;
            padding: 4px;
            font-size: 13px;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif
        }


        .list-test {
            height: 1000px;
        }

        .side {
            background: transparent;
            border: none;
            margin-top: 5px
        }

        .flex-container-side {
            display: flex;
            flex-direction: row;
            font-size: 16px;
            text-align: center
        }


        .flex-item-desktop-left {
            flex: 50%;
            margin-top: 14px;
            margin-right: 1.5px;


        }

        .flex-item-desktop-right {
            flex: 50%;
            margin-top: 14px;
            margin-left: 1.5px;


        }

        .flex-item-desktop-right:hover {
            animation-name: hoverWobbleVertical;
            animation-duration: 1s;
            animation-timing-function: ease-out;
            animation-iteration-count: 1;
            transform-origin: center bottom;
            border-radius: 10px;
            transition-delay: 30ms;
            color: black;
        }


        .flex-item-left {
            flex: 20%
        }

        .flex-item-center {
            flex: 60%;
            padding-top: 26px;
            padding-bottom: 7px
        }

        .flex-item-left-search {
            flex: 90%
        }

        .flex-item-right-search {
            flex: 10%;

        }

        .flex-item-right {
            padding-top: 26px;
            padding-bottom: 7px;
            flex: 20%
        }

        .flex-item-dropdown-left {
            padding-top: 12px;
            flex: 80%;
            border-bottom: solid #FAF0D7 1px;
            border-right: solid #FAF0D7 1px
        }

        .flex-item-dropdown-right {
            flex: 20%
        }

        .header {
            background-color: #FFF349;
            padding: 1px
        }

        .marquee-text {
            height: 30px;
            display: block;
            line-height: 30px;
            overflow: hidden;
            position: relative
        }

        .marquee-text:before,
        .marquee-text:after {
            content: '';
            position: absolute;
            width: 5px;
            height: 100%;
            background: #FFF349;
            top: 0;
            z-index: 2
        }

        .marquee-text:before {
            left: 0
        }

        .marquee-text:after {
            right: 0
        }

        .marquee-text div {
            display: block;
            width: 200%;
            height: 30px;
            line-height: 30px;
            position: absolute;
            overflow: hidden;
            font-size: 14px;
            color: #3C4048;
            z-index: 1;
            font-weight: 700;
            animation: marquee 15s linear infinite
        }

        .marquee-text span {
            float: left;
            width: 50%
        }

        .marquee-text:hover div {
            animation-play-state: paused
        }

        @keyframes  marquee {
            0% {
                left: 0
            }

            100% {
                left: -100%
            }
        }

        .on-768px,
        .on-425px {
            display: none
        }


        @media  screen and (max-width:1024px) {
            .marquee-text div {
                animation: marquee 10s linear infinite
            }
        }

        @media  screen and (max-width:768px) {
            .marquee-text div {
                animation: marquee 8s linear infinite
            }

            .on-768px {
                display: block
            }

            .on-425px,
            .on-1440px {
                display: none
            }


        }

        @media  screen and (max-width:425px) {
            .marquee-text div {
                animation: marquee 5s linear infinite
            }

            .on-425px {
                display: block
            }

            .on-768px,
            .on-1440px {
                display: none
            }
        }

        body {
            font: "Bitstream Vera Serif Bold", serif
        }

        @keyframes  slide-in {
            0% {
                top: -100%
            }

            100% {
                top: 50%
            }
        }

        .link-container {
            display: flex;
            justify-content: center;
            font-size: var(--x-large-font);
            padding: 0;
            width: 100%
        }

        .grid-container {
            display: grid;
            grid: 70px / auto auto auto;
            padding: 5px
        }

        .grid-container>div {
            text-align: center;
            padding: 3px
        }

        .item2 {
            justify-content: center;
            text-align: center;
            padding-top: 3px;
            padding-bottom: 3px
        }

        .link-container a {
            width: 50%;
            text-align: center;
            padding: 15px 20px;
            text-transform: uppercase;
            color: #fff;
            text-decoration: none
        }

        .register-button {
            background-image: linear-gradient(rgb(104, 2, 2), rgb(203, 0, 0));
            background-color: initial
        }

        .login-button {
            background-image: linear-gradient(rgb(0, 12, 153), rgb(7, 71, 102));
            background-color: initial
        }

        * {
            font-family: sans-serif;
            box-sizing: border-box
        }

        p {
            margin: 0
        }

        a:hover {
            text-decoration: none;
            color: #fff
        }

        .container {
            width: 100%;
            margin-left: auto;
            margin-right: auto
        }

        .adv {
            justify-content: center;
            display: flex;
            flex-wrap: wrap
        }

        img {
            vertical-align: middle;
            border-style: none
        }

        .title {
            display: flex;
            justify-content: center
        }

        .title-text {
            color: #fff;
            font-size: 2.5rem
        }

        .container.footer.text-center {
            padding-top: 2px;
            color: #929AAB
        }

        .slot {
            display: block
        }

        .slot .slot-sidebar {
            padding-right: 10px;
            padding-left: 0;
            margin-top: -15px;
            background-color: #000;
            width: 100%;
            flex: none;
            max-width: 100%;
            padding-right: 0
        }

        .btn-provider:hover {
            background-color: #644c1c
        }

        .slot-sidebar-nav {
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: nowrap;
            padding-left: 0;
            margin-bottom: 0;
            list-style: none
        }

        .slot-sidebar-nav>li {
            border-bottom: 1px solid #0092b1;
            width: 100%;
            position: relative;
            display: block;
            border-bottom: none;
            padding: 6px;
            background: linear-gradient(to bottom, #f5cc00 0%, #ffd400 46%, #fff702 100%)
        }

        .slot-sidebar-nav>li>a {
            color: #fff;
            font-size: 13px;
            padding: 7px 10px;
            display: block;
            background-color: #000
        }

        .btn-provider {
            text-align: center;
            display: block
        }

        .enter {
            display: none
        }

        .btn-provider span {
            position: unset
        }

        .active {
            background-color: #0092b1
        }


        .slot .content {
            float: none;
            width: 100%;
            padding: 10px;
            flex: 0 0 100%;
            max-width: 100%;
            background-color: #1d1d1d;
            border-radius: 4px
        }

        .wrapper {
            width: 100%;
            padding: 0;
            overflow: hidden;
            position: relative;
            z-index: 0
        }

        .card {
            float: left;
            width: 25%;
            background: 0 0;
            border: none;
            text-align: center;
            position: relative
        }

        .card-content {
            margin: 5px;
            color: #fff;
            font-size: 12px;
            background-color: rgba(219, 219, 219, .21);
            overflow: hidden;
            position: relative;
            padding: 5px;
            border-radius: 5px
        }

        .percent {
            height: 18px;
            display: flex;
            overflow: hidden;
            line-height: 0;
            font-size: .75rem;
            background-color: gray;
            position: relative;
            z-index: 1;
            border-radius: 18px;
            margin: 4px auto
        }

        .percent p {
            z-index: 15;
            position: absolute;
            text-align: center;
            width: 100%;
            font-size: 14px;
            font-weight: 700;
            transform: translateY(8px);
            color: #000
        }

        .percent-bar {
            background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
            background-size: 1rem 1rem;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: column;
            flex-direction: column;
            -ms-flex-pack: center;
            justify-content: center;
            overflow: hidden;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            transition: width .6s ease;
            -webkit-animation: progress-bar-stripes 1s linear infinite;
            animation: progress-bar-stripes 1s linear infinite;
            z-index: 10
        }

        .card-content a.hover-btn {
            display: block
        }

        .hover-btn {
            position: absolute;
            width: 212px;
            opacity: 0;
            background-color: rgba(0, 0, 0, .9);
            transition: all .1s ease-in-out;
            z-index: 10;
            height: 148px;
            border-radius: 3px
        }

        .image {
            width: 100%;
            height: 100%
        }

        .maintenance {
            position: absolute;
            background-color: rgba(0, 0, 0, .5);
            z-index: 10;
            width: 100%;
            height: 100%;
            color: #fff;
            text-align: center;
            margin: -6px
        }

        .maintenance p {
            position: relative;
            top: 40%;
            font-size: 13px
        }

        .play-btn {
            font-size: 15px;
            text-decoration: none;
            font-weight: 700;
            text-align: center;
            align-items: center;
            margin-top: 42%;
            padding: 8px;
            display: block;
            margin-left: 30px;
            margin-right: 30px;
            margin: 42% 30px;
            background-color: #eeec36;
            background-image: linear-gradient(19deg, #eeec36 0%, #f79e1a 100%);
            border-radius: 4px;
            transition: all .3s ease
        }

        .play-btn:hover {
            color: #000;
            background-color: #e0de31;
            background-image: linear-gradient(19deg, #e0de31 0%, #d6860f 100%);
            transition-duration: 3s
        }

        .img-zoom {
            transition: all .45s ease-in-out;
            border-radius: 5px
        }

        .ygg-img {
            border: 5px solid #2f2f2f
        }

        .hover-btn:hover {
            opacity: 100%
        }

        .hover-btn:hover~.img-zoom {
            position: relative
        }

        .short {
            display: none
        }

        .next-btn {
            background: linear-gradient(to bottom, #242424 0%, #515151 46%, #242424 100%);
            width: 15%
        }

        .mySlides {
            display: none
        }

        .next-btn {
            background-color: #292a2b;
            border: none;
            color: #fff
        }

        @media(min-width:576px) {
            .container {
                max-width: 540px
            }
        }

        @media(min-width:768px) {
            .container {
                max-width: 720px
            }
        }

        @media(min-width:992px) {
            .container {
                max-width: 960px
            }
        }

        @media(min-width:1200px) {
            .container {
                max-width: 1140px
            }
        }

        @media(max-width:992px) {
            .slot-sidebar-nav {
                flex-wrap: nowrap
            }

            .slot-sidebar {
                width: 100%;
                flex: none;
                max-width: 100%;
                padding-right: 0
            }

            .content {
                float: none;
                width: 100%;
                flex: none;
                max-width: 100%
            }

            .card {
                width: 33.333333333333333%
            }

            .hover-btn {
                display: none
            }

            .play-btn {
                display: none
            }

            .hover-btn:hover~.img-zoom {
                transform: scale(1);
                position: relative
            }

            .btn-provider {
                text-align: center;
                display: block
            }

            .enter {
                display: block
            }

            .btn-provider span {
                position: unset
            }

            .btn-provider i {
                margin: 0
            }

            .slot-sidebar-nav li {
                border-bottom: none
            }

            .slot-sidebar-nav li a p {
                font-size: 13px
            }

            .slot-sidebar-nav li a img {
                height: 37.5px
            }

            .maintenance p {
                font-size: 5px
            }
        }

        html {
            font-family: -apple-system, system-ui, BlinkMacSystemFont, segoe ui, Roboto, helvetica neue, Arial, sans-serif;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%
        }

        a,
        body,
        center,
        div,
        em,
        kolongramen,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        header,
        html,
        iframe,
        img,
        li,
        menu,
        nav,
        ol,
        p,
        span,
        table,
        tbody,
        td,
        tfoot,
        th,
        thead,
        tr,
        ul {
            font-family: -apple-system, system-ui, BlinkMacSystemFont, segoe ui, Roboto, helvetica neue, Arial, sans-serif;
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            vertical-align: baseline
        }

        a,
        a:active,
        a:focus {
            outline: 0;
            text-decoration: none
        }

        a {
            color: #fff
        }

        * {
            padding: 0;
            margin: 0;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            box-sizing: border-box
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            margin-bottom: .5rem
        }

        p {
            margin: 0 0 10px
        }

        p {
            margin-top: 0;
            margin-bottom: 1rem
        }

        .clear {
            clear: both
        }

        .konten-bola {
            text-align: center
        }

        .align-middle {
            vertical-align: middle
        }

        body.rtpslot {
            background: url("https://cdn.areabermain.club/assets/cdn/az3/2024/05/23/20240523/3171c07c0fd58c236645035fca1be7a4/gengtoto-bg-2024.jpg");
            background-position: center top;
            background-attachment: fixed;
            background-repeat: no-repeat;
            background-size: cover;
            top: -12px
        }

        main {
            background: rgb(0, 0, 0);
            background: linear-gradient(90deg, rgba(0, 0, 0, .2) 0%, rgba(0, 0, 0, .2) 100%);
            border-bottom-left-radius: .5rem;
            border-bottom-right-radius: .5rem;
            margin: 0 auto;
            max-width: 960px
        }

        .container {
            padding: 1rem
        }


        @keyframes  blinking {
            0% {
                border: 5px solid #fff
            }

            100% {
                border: 5px solid #fdbf02
            }
        }

        .bola-casino {
            animation-name: blinker;
            animation-duration: 1s;
            animation-timing-function: linear;
            animation-iteration-count: infinite
        }

        .anim {
            animation: blinkings 1s infinite
        }

        @keyframes  blinkings {
            0% {
                border: 2px solid #fff
            }

            100% {
                border: 2px solid #fdbf02
            }
        }

        @media(min-width:768px) {
            .container {
                max-width: 720px
            }
        }

        @media(min-width:992px) {
            .container {
                max-width: 960px
            }
        }

        .row {
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px
        }

        .p-0 {
            padding: 0
        }

        .col-md-12,
        .col-md-4,
        .col-md-6,
        .col-md-8,
        .col-xs-6 {
            position: relative;
            width: 100%;
            padding-right: 15px;
            padding-left: 15px
        }

        .col-xs-6 {
            float: left;
            width: 50%
        }

        @media(min-width:768px) {
            .col-md-4 {
                -ms-flex: 0 0 33.333333%;
                flex: 0 0 33.333333%;
                max-width: 33.333333%
            }

            .col-md-6 {
                -ms-flex: 0 0 50%;
                flex: 0 0 50%;
                max-width: 50%
            }

            .col-md-8 {
                -ms-flex: 0 0 66.666667%;
                flex: 0 0 66.666667%;
                max-width: 66.666667%
            }

            .col-md-12 {
                -ms-flex: 0 0 100%;
                flex: 0 0 100%;
                width: 100%
            }

            .order-first {
                -ms-flex-order: -1;
                order: -1
            }

            .logomobi {
                display: none
            }

            .logform {
                padding-top: 2rem
            }

            .nopadding {
                padding: 0
            }
        }

        @media(max-width:768px) {
            .logo {
                display: none
            }

            .navbar {
                position: fixed
            }

            .border-bt {
                border-bottom: 1px solid #fdbf02;
                border-top: 1px solid #fdbf02;
                padding: 5px 15px
            }
        }

        .pt-1,
        .py-1 {
            padding-top: .25rem
        }

        .pb-1,
        .py-1 {
            padding-bottom: .25rem
        }

        .pt-2,
        .py-2 {
            padding-top: .5rem
        }

        .pb-2,
        .py-2 {
            padding-bottom: .5rem
        }

        .mt-2,
        .my-2 {
            margin-top: .5rem
        }

        .my-2 {
            margin-top: .5rem
        }

        .mb-2 {
            margin-bottom: 1rem
        }

        .mb-4 {
            margin-bottom: 4rem
        }

        .bartender,
        .my-3 {
            margin-top: .75rem
        }

        .mb-3,
        .my-3 {
            margin-bottom: .75rem
        }

        .mt-4 {
            margin-top: 1.1rem
        }

        .mt-5,
        .my-5 {
            margin-top: 2rem
        }

        .mb-5,
        .my-5 {
            margin-bottom: 2rem
        }

        .pb-5 {
            padding-bottom: 1.25rem
        }

        .mx-5 {
            margin-left: .75rem;
            margin-right: .75rem
        }

        .pt-3 {
            padding-top: 1rem
        }

        .pt-5 {
            padding-top: 2rem
        }

        .navbar {
            background-color: #000;
            right: 0;
            left: 0;
            z-index: 1030;
            width: 100%;
            float: left;
            padding: 5px
        }

        .bg-blue {
            background-color: #020202
        }

        .bottom {
            float: left;
            width: 100%
        }

        .konten {
            color: #fff;
            padding: 20px 30px;
            border-radius: 5px;
            font-family: -apple-system, system-ui, BlinkMacSystemFont, segoe ui, Roboto, helvetica neue, Arial, sans-serif;
            box-shadow: 0 0 10px 6px #f9f303
        }

        .konten h1 {
            font-size: 1.5em
        }

        .konten h2 {
            font-size: 1.3em
        }

        .konten h3 {
            font-size: 1.1em
        }

        .konten p {
            font-size: 1em
        }

        .konten a {
            color: #fdbf02
        }

        .list {
            margin-bottom: 1rem
        }

        .kolongramen {
            text-decoration: none;
            color: #fff
        }

        .kolongramen a {
            color: #fdbf02
        }

        .slide {
            width: 100%;
            border-radius: 4px;
            box-shadow: 0 0 3px 0 #1a1a1a
        }

        .lc-atribut {
            border: 2px solid #f7a303;
            border-radius: 4px;
            box-shadow: 0 0 5px 0 #f7a303
        }

        ul {
            color: #fff;
            text-align: left
        }

        .faq-label {
            display: flex;
            font-size: 1.5em;
            justify-content: space-between;
            padding: 1em;
            margin: 12px 0 0;
            background: #0095ff
        }

        .faq-answer {
            padding: 1em;
            font-size: 1.19em;
            color: #fff;
            text-align: justify;
            background: #212121;
            transition: all .35s
        }

        .qiuonline {
            text-align: center;
            font-size: 1.5em;
            justify-content: space-between;
            padding: 1em;
            margin: 12px 0 0;
            background: #fdbf02
        }

        .list {
            margin-bottom: 1rem
        }

        .silau {
            border-radius: 10px;
            box-shadow: 0 0 10px 2px #965800;
            animation: blinking .3s infinite;
            transition: all .1s
        }

        .silau:hover {
            opacity: 1
        }

        .tengah {
            width: 40%;
            margin: auto
        }

        .button {
            display: inline;
            align-items: center;
            background: #000;
            width: 100%;
            border-radius: 5px;
            height: 38px;
            cursor: pointer;
            padding: 5px 20px;
            max-width: 128px;
            color: rgb(255 255 255);
            font-weight: 700;
            font-family: -apple-system, system-ui, BlinkMacSystemFont, segoe ui, Roboto, helvetica neue, Arial, sans-serif;
            text-transform: uppercase;
            text-decoration: none;
            transition: background .3s, transform .3s, box-shadow .3s;
            will-change: transform;
            min-width: 80px;
            border: 0 solid rgb(255 255 255);
            line-height: 12px;
            animation: blinking .5s infinite;
            transition: all .4s
        }

        .button:hover {
            color: #e7b10c;
            font-weight: 700;
            text-decoration: none;
            background: rgb(255 255 255);
            cursor: pointer;
            box-shadow: 0 4px 17px rgba(0, 0, 0, .2);
            transform: translate3d(0, -2px, 0);
            border: 2px solid #e7b10c
        }

        .button:active {
            box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1);
            transform: translate3d(0, 1px, 0)
        }

        a {
            background-color: transparent
        }

        a:active,
        a:hover {
            outline: 0
        }

        h1 {
            margin: .67em 0;
            font-size: 2em
        }

        img {
            border: 0
        }

        @media  print {

            *,
            :after,
            :before {
                color: #000;
                text-shadow: none;
                background: 0 0;
                -webkit-box-shadow: none;
                box-shadow: none
            }

            a,
            a:visited {
                text-decoration: underline
            }

            a[href]:after {
                content: " (" attr(href) ")"
            }

            img,
            tr {
                page-break-inside: avoid
            }

            img {
                max-width: 100%
            }

            h2,
            h3,
            p {
                orphans: 3;
                widows: 3
            }

            h2,
            h3 {
                page-break-after: avoid
            }
        }

        .list {
            margin-bottom: 1rem
        }

        .text-center {
            text-align: center
        }

        p#breadcrumbs {
            color: #fff;
            text-align: center
        }

        .konten ul li {
            list-style-type: square
        }

        .konten li {
            margin: 5px 30px 10px;
            text-align: justify;
            color: #fff
        }

        .betting-list-card__list-item-1 {
            border-bottom: 4px solid #f6a841
        }

        @media  only screen and (min-width:1200px) {
            .betting-list-card__title {
                width: 70%
            }

            .betting-list-card__title {
                order: 2;
                padding-left: 18px;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between
            }

            .text-white {
                color: #fff
            }

            .layout-list-home {
                height: 274px;
            }
        }

        @media  only screen and (min-width:998px) {
            .img-zoom {
                height: 148px
            }

            .layout-list {
                height: 118px;
            }

            .layout-list-home {
                height: 274px;
            }


            .flex-container-side-desktop {
                display: flex;
                flex-direction: row;
                font-size: 16px;
                text-align: center;
                align-items: center;
            }

            .side-bar {
                width: 310px;
                background-color: rgba(0, 0, 0, .90);
                border-radius: 10px 0px 0px 10px;
            }

        }

        @media  only screen and (max-width:998px) {


            .grid-container {
                display: grid;
                grid: 55px / auto auto auto;
                padding: 5px
            }

            .img-zoom {
                height: 132px
            }

            .flex-container-side-desktop {
                display: none;
            }

            .side-bar {
                width: 240px;
                background-color: rgba(0, 0, 0, .90);
                border-radius: 10px 0px 0px 10px;
            }

            .card-content {
                font-size: 18px
            }

            .hover-btn {
                position: absolute;
                width: 0;
                opacity: 0;
                background-color: transparent;
                transition: all .1s ease-in-out;
                z-index: 10;
                height: 148px;
                border-radius: 3px
            }

            .layout-list-home {
                height: 223px;
            }

            .layout-list {
                height: 115px;
            }
        }

        @media  only screen and (max-width:768px) {

            .grid-container {
                display: grid;
                grid: 55px / auto auto auto;
                padding: 5px
            }

            .img-zoom {
                height: 120px
            }

            .card-content {
                font-size: 16px
            }

            .hover-btn {
                position: absolute;
                width: 0;
                opacity: 0;
                background-color: transparent;
                transition: all .1s ease-in-out;
                z-index: 10;
                height: 148px;
                border-radius: 3px
            }

            .layout-list-home {
                height: 223px;
            }

            .layout-list {
                height: 114px;
            }
        }

        @media  only screen and (max-width:568px) {

            .grid-container {
                display: grid;
                grid: 55px / auto auto auto;
                padding: 5px
            }

            .img-zoom {
                height: 76px;
                object-fit: contain
            }

            .m-80 {
                width: 80%
            }

            .card-content {
                font-size: 11px;
                padding: 3.5px
            }

            .hover-btn {
                position: absolute;
                width: 0;
                opacity: 0;
                background-color: transparent;
                transition: all .1s ease-in-out;
                z-index: 10;
                height: 148px;
                border-radius: 3px
            }

            .layout-list-home {
                height: 223px;
            }

            .layout-list {
                height: 114px;
            }
        }

        @media  only screen and (max-width:400px) {

            .grid-container {
                display: grid;
                grid: 55px / auto auto auto;
                padding: 5px
            }

            .img-zoom {
                height: 67px;
                object-fit: contain
            }

            .card-content {
                font-size: 10px;
                padding: 3px
            }

            .hover-btn {
                position: absolute;
                width: 0;
                opacity: 0;
                background-color: transparent;
                transition: all .1s ease-in-out;
                z-index: 10;
                height: 148px;
                border-radius: 3px
            }

            .layout-list-home {
                height: 223px;
            }

            .layout-list {
                height: 114px;
            }
        }

        #menutb td {
            background: #1a1a1a
        }

        #menutb td:hover {
            background: #141414;
            border-radius: 10px
        }

        a:hover>.play-btn {
            text-decoration: none;
            color: #000
        }

        .toplist__label {
            color: #1f2426;
            font-weight: 700;
            font-style: normal;
            font-stretch: normal;
            line-height: 1.58;
            color: #1f2426;
            font-size: 28px;
            margin-top: 14px
        }

        .content.marq {
            background-color: transparent
        }

        .content.marq-header {
            background-color: #f3c314;
        }

        .content.bgrtp {
            background-color: #000
        }

        h3.gacor {
            padding-top: 1px;
            color: #000;
            border: 1px solid #f3c314;
            border-radius: 5px;
            background-image: radial-gradient(circle 763px at 18.3% 24.1%, rgba(255, 249, 137, 1) 7.4%, rgba(226, 183, 40, 1) 58.3%)
        }

        .m-hide {
            display: none
        }

        .search {
            border: 1px solid grey;
            border-radius: 5px;
            height: 25px;
            width: 100%;
            padding: 2px 23px 2px 30px;
            outline: 0;
            background-color: #f5f5f5
        }

        .search-icon {
            position: absolute;
            top: 6px;
            left: 8px;
            width: 14px
        }

        .clear-icon {
            position: absolute;
            top: 7px;
            right: 8px;
            width: 12px;
            cursor: pointer;
            visibility: hidden
        }

        .search:hover,
        .search:focus {
            border: 1px solid #009688;
            background-color: #fff
        }

        .app-footer {
            border-radius: 5px;
            background-color: #1a1a1a;
            opacity: .9;
            padding: 1rem;
            margin-bottom: 11px
        }

        .app-footer section {
            border-top: 1px solid hsla(0, 0%, 100%, .1);
            border-bottom: 1px solid hsla(0, 0%, 100%, .1)
        }

        .app-footer__partners h5 {
            color: #f79236;
            text-align: center;
            margin-bottom: 1rem
        }

        .app-footer__partners ul {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(2rem, 1fr))
        }

        .app-footer__partners li:hover {
            background-image: linear-gradient(to bottom, #fdf571 0%, #d6b75f 100%);
            border-radius: 1rem
        }

        ul {
            list-style-type: none
        }

        .app-footer__partners ul li {
            flex-grow: 1;
            min-width: 15%;
            align-items: center
        }

        .app-footer__partners ul li a {
            padding: .5rem;
            font-size: .75rem;
            white-space: nowrap;
            color: hsla(0, 0%, 100%, .6);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center
        }

        .app-footer__partners ul li a:hover {
            color: #000
        }

        @media  screen and (max-width:600px) {
            h3.gacor {
                font-size: 22px
            }
        }

        .box-logo {
            padding: 1rem;
            border-radius: 5px;
            background: rgb(12, 68, 162);
            background: linear-gradient(90deg, rgba(12, 68, 162, 0.82) 0%, rgba(0, 143, 214, 0.82) 100%);
        }

        #jamgacor {
            position: relative;
            z-index: 100
        }

        #xpola {
            position: relative;
            z-index: 99;
            min-height: 90px
        }

        .d-block {
            display: block
        }

        .bg-dark {
            background-color: #1a1a1a;
            border-radius: 7px
        }

        .rounded {
            border-radius: .25rem
        }

        .rounded-pill {
            border-radius: 50rem
        }

        .p-1 {
            padding: .25rem
        }

        .mt-md-1,
        .my-md-1 {
            margin-top: .25rem
        }

        .mb-0,
        .my-0 {
            margin-bottom: 0
        }

        .text-danger {
            color: #dc3545
        }

        .text-success {
            color: #28a745
        }

        #bPola {
            z-index: 100
        }

        .hidden {
            display: none
        }

        .px-2 {
            padding-top: 1rem;
            padding-bottom: 1rem
        }

        .f-1 {
            font-size: 14px
        }

        .p-2 {
            padding-top: 20px;
            padding-bottom: 2px;
            padding-left: 2px;
            padding-right: 2px
        }

        #xpola:before {
            content: "POLA";
            position: relative;
            display: block;
            /* vertical-align: -50%; */
            margin: -18px 0px 4px -4px;
            padding: 4px;
            left: 0px;
            width: 100%;
            background: linear-gradient(to bottom, #919191 0, #494949 100%);
            border-radius: 40px;
            /* box-shadow: 0px 1px 0px #e2d176; */
            font-size: 14px;
            transform: scale(0.7);
            color: white;
            font-weight: 600;
        }


        .vendor-list>div[role="list"] {
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            align-items: center
        }

        .vendor-list>div[role="list"]>div[role="listitem"] {
            flex-grow: 1;
            width: calc(100% / 3)
        }

        .form-control {
            width: 100%;
            box-sizing: border-box;
            padding: 6px 10px;
            border: 1px solid #000;
            border-radius: 6px;
            margin-bottom: 8px
        }

        .form-inline {
            display: flex
        }

        .icon-search {
            display: flex;
            align-items: center;
            border: 1px solid #000;
            border-right-color: transparent;
            padding: 6px 10px;
            background-color: #fff;
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px
        }

        .form-inline>.form-control {
            border-left: 0;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            margin-left: -10px
        }

        .pbar {
            margin-top: 5px;
            background-color: #e9ecef;
            border-radius: 18px;
            font-size: .75rem;
            height: 18px;
            overflow: hidden;
            position: relative
        }

        .pbar>.pbar-bg {
            position: relative;
            background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
            background-size: 1rem 1rem;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center
        }

        .pbar>.pbar-text {
            position: absolute;
            left: 50%;
            top: 50%;
            color: #000;
            font-weight: 700;
            transform: translate(-50%, -50%)
        }

        .yellow {
            background-color: #ffc107
        }

        .green {
            background-color: #28a745
        }

        .red {
            background-color: #dc3545
        }

        .white {
            color: #fff
        }

        .info {
            color: #fff;
            font-style: italic;
            text-align: right
        }

        .navigation {
            display: flex;
            justify-content: space-around;
            align-items: center
        }

        .btn-prev,
        .btn-next {
            margin-top: 35px;
            padding: 6px 10px;
            background-color: #FFF1DC;
            border: 1px solid #E8D5C4;
            color: black;
            font-weight: bold;
            border-radius: 8px;
            font-size: 14px;
            transition: all .3s;
            cursor: pointer;
            box-shadow: 5px 5px 5px 5px rgba(0, 0, 0, .15)
        }

        .flex {
            display: flex
        }

        .justify-between {
            justify-content: space-between
        }

        .align-items-center {
            align-items: center
        }

        .menu-side.back {
            word-spacing: 10px
        }

        /* Optimized loading styles */
        .optimized-list {
            contain: layout style paint;
            will-change: transform;
        }

        .img-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 150px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-spinner-large {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-placeholder {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error-fallback {
            text-align: center;
            padding: 20px;
            color: #d32f2f;
            background: #ffebee;
            border-radius: 8px;
            margin: 10px;
        }

        .error-fallback button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }

        /* Optimized card styles */
        .card {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
            transition: transform 0.2s ease-out;
        }

        .card:hover {
            transform: translateY(-2px) scale(1.02);
        }

        /* Lazy loading optimization */
        amp-img[loading="lazy"] {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        amp-img[loading="lazy"].i-amphtml-layout {
            opacity: 1;
        }

        /* Progress bar optimization */
        .pbar {
            position: relative;
            overflow: hidden;
        }

        .pbar-bg {
            transition: width 0.5s ease-out;
            transform: translateZ(0);
        }

        /* Pola pattern styles */
        .pola-pattern {
            margin: 2px 0;
            font-size: 12px;
        }

        .icon-check {
            color: #00ff00 !important;
        }

        .icon-close {
            color: #ff0000 !important;
        }

        .pola-unavailable {
            color: #ffcc00;
            font-size: 11px;
        }
    </style>
<link rel="preconnect" href="https://statics.hokibagus.club"><script async="" custom-element="amp-auto-lightbox" data-script="amp-auto-lightbox" i-amphtml-inserted="" type="text/plain" data-src-preserved="https://cdn.ampproject.org/rtv/012508201830000/v0/amp-auto-lightbox-0.1.js"></script><script async="" custom-element="amp-loader" data-script="amp-loader" i-amphtml-inserted="" type="text/plain" data-src-preserved="https://cdn.ampproject.org/rtv/012508201830000/v0/amp-loader-0.1.js"></script><link rel="preconnect" href="https://object-d001-cloud.cloudstoragesharingservice.com"><link rel="preconnect" href="https://cdn.areabermain.club"><link rel="preconnect" href="https://rtpslotgeng414.com"><link rel="preconnect" href="http://localhost"><link rel="preconnect" href="https://wl2.loobygameshub.com"></head>

<body class="rtpslot amp-mode-mouse" style="opacity: 1; visibility: visible; animation: auto ease 0s 1 normal none running none;">
    <amp-install-serviceworker src="/serviceworker.js" layout="nodisplay" class="i-amphtml-element i-amphtml-layout-nodisplay i-amphtml-built" hidden="" i-amphtml-layout="nodisplay"></amp-install-serviceworker>
    <div class="flex-container-side">
     <div class="flex-item-left"></div>
     <div class="flex-item-center">
         <amp-img src="https://statics.hokibagus.club/gengtoto/images/rtpslot/logo_gengtoto.png" height="33px" width="100px" alt="Logo" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 100px; height: 33px; --loader-delay-offset: 110ms !important;">
         <img decoding="async" alt="Logo" src="assets/gengtoto_images_rtpslot_logo_gengtoto.png.d49e7eb2c4.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
     </div>
     <div class="flex-item-right">
                      <button class="side" on="tap:sidebar1">
                 <span class="material-symbols-rounded" style="font-size:28px; color:#FAF0D7">menu</span>
             </button>
             <amp-sidebar class="side-bar i-amphtml-element i-amphtml-layout-nodisplay i-amphtml-overlay i-amphtml-scrollable i-amphtml-built" id="sidebar1" layout="nodisplay" side="right" hidden="" i-amphtml-layout="nodisplay" role="menu" tabindex="-1"><button class="i-amphtml-screen-reader" tabindex="-1">Close the sidebar</button>
                 <amp-list layout="fixed-height" height="915" src="/jenisAll.json" binding="no" items="." single-item="" class="i-amphtml-element i-amphtml-layout-fixed-height i-amphtml-layout-size-defined i-amphtml-built" i-amphtml-layout="fixed-height" style="height: 915px;">
                     <template type="amp-mustache">
                         <amp-nested-menu layout="fill">
                             <ul>
                                 <li>
                                     <div class="menu-side"><a class="menu-side" href="/">Home</a></div>
                                 </li>
                                                                      <li>
                                         <div class="menu-side">
                                             <a class="menu-side" href="http://rtpslotgeng414.com/side/payment">Bukti Bayar</a>
                                         </div>
                                     </li>
                                                                                                       <li>
                                         <div class="menu-side" amp-nested-submenu-open="">Prediksi Togel</div>
                                         <div amp-nested-submenu="">
                                             <ul>
                                                 <li>
                                                     <div class="menu-side back" amp-nested-submenu-close="">
                                                         ← Kembali
                                                     </div>
                                                 </li>
                                                 <li>
                                                     <div class="menu-side-type">
                                                         <a class="menu-side-type" href="http://rtpslotgeng414.com/prediksi">Prediksi Pools</a>
                                                     </div>
                                                 </li>
                                                 {{ #items }}
                                                 <li>
                                                     <div class="menu-side-type">
                                                         <a class="menu-side-type" href="http://rtpslotgeng414.com/prediksi/pasaran/{{ nama_togel }}">Prediksi
                                                             {{ nama_togel }}</a>
                                                     </div>
                                                 </li>
                                                 {{ /items }}
                                             </ul>
                                         </div>
                                     </li>
                                                              </ul>
                         </amp-nested-menu>
                     </template>
                 <div class="i-amphtml-fill-content i-amphtml-replaced-content"></div></amp-list>
             <button class="i-amphtml-screen-reader" tabindex="-1">Close the sidebar</button></amp-sidebar>
              </div>
 </div>
    <header class="header">
     <div class="marquee-text on-1440px">
         <div>
             <span>SELAMAT DATANG DI GENGTOTO BANDAR SLOT TERPERCAYA INDONESIA | BERAPAPUN KEMENANGAN ANDA PASTI KAMI BAYAR | BONUS TURNOVER 0.5% MENANTI ANDA KHUSUS SLOT GAMES.</span>
             <span>SELAMAT DATANG DI GENGTOTO BANDAR SLOT TERPERCAYA INDONESIA | BERAPAPUN KEMENANGAN ANDA PASTI KAMI BAYAR | BONUS TURNOVER 0.5% MENANTI ANDA KHUSUS SLOT GAMES.</span>
         </div>
     </div>

     <div class="marquee-text on-768px">
         <div>
             <span>SELAMAT DATANG DI GENGTOTO BANDAR SLOT TERPERCAYA INDONESIA | BERAPAPUN KEMENANGAN ANDA PASTI KAMI BAYAR | BONUS TURNOVER 0.5% MENANTI ANDA KHUSUS SLOT GAMES.</span>
             <span>SELAMAT DATANG DI GENGTOTO BANDAR SLOT TERPERCAYA INDONESIA | BERAPAPUN KEMENANGAN ANDA PASTI KAMI BAYAR | BONUS TURNOVER 0.5% MENANTI ANDA KHUSUS SLOT GAMES.</span>
         </div>
     </div>

     <div class="marquee-text on-425px">
         <div>
             <span>SELAMAT DATANG DI GENGTOTO BANDAR SLOT TERPERCAYA INDONESIA | BERAPAPUN KEMENANGAN ANDA PASTI KAMI BAYAR | BONUS TURNOVER 0.5% MENANTI ANDA KHUSUS SLOT GAMES.</span>
             <span>SELAMAT DATANG DI GENGTOTO BANDAR SLOT TERPERCAYA INDONESIA | BERAPAPUN KEMENANGAN ANDA PASTI KAMI BAYAR | BONUS TURNOVER 0.5% MENANTI ANDA KHUSUS SLOT GAMES.</span>
         </div>
     </div>
 </header>
    <main>
        <div class="content marq">
            <div class="container">
                    <a href="https://geng33041.com/">
        <amp-img src="https://statics.hokibagus.club/gengtoto/rtpslot/gengtoto_slider_rtpslot.webp" width="900" height="510" layout="responsive" tabindex="0" alt="banner-img" role="button" class="banner i-amphtml-element i-amphtml-layout-responsive i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="responsive" style="--loader-delay-offset: 110ms !important;"><i-amphtml-sizer slot="i-amphtml-svc" style="padding-top: 56.6667%;"></i-amphtml-sizer><img decoding="async" alt="banner-img" src="assets/gengtoto_rtpslot_gengtoto_slider_rtpslot.webp.3ba7ea1aa6.webp" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
    </a>

    <section class="slot">
        <div id="wrapper" class="wrapper">
            <div class="text-center mt-4 box-logo">
                <a href="/">
                    <amp-img src="https://statics.hokibagus.club/gengtoto/images/rtpslot/logo_gengtoto.png" height="50px" width="180px" alt="Logo" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 180px; height: 50px; --loader-delay-offset: 110ms !important;"><img decoding="async" alt="Logo" src="assets/gengtoto_images_rtpslot_logo_gengtoto.png.d49e7eb2c4.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                </a>
            </div>

            <div class="flex-container-side-desktop">
                <a class="flex-item-desktop-left" href="http://rtpslotgeng414.com/side/payment">
                    <div class="box-1">
                        <div class="btn btn-one">
                            <span>BUKTI BAYAR</span>
                        </div>
                    </div>
                </a>
                <a class="flex-item-desktop-right" href="http://rtpslotgeng414.com/prediksi">
                    <div class="box-1">
                        <div class="btn btn-one">
                            <span>PREDIKSI TOGEL</span>
                        </div>
                    </div>
                </a>
            </div>

            <h3 class="toplist__label text-center gacor">RTP Slot Terbaik Hari Ini</h3>


            <h3 class="text-center mb-2" style="padding-top: 4px;color:#FAF0D7;font-size:18px">
                Rabu, 03 September 2025
            </h3>


            <amp-list layout="responsive" width="200px" height="125px" class="layout-list-home i-amphtml-element i-amphtml-layout-responsive i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" src="/jenisVendor.json" binding="no" items="." single-item="" i-amphtml-layout="responsive" style="height: 406px;">
                <template type="amp-mustache">
                    <div class="app-footer">
                        <section class="app-footer__partners">
                            <div class="grid-container">
                                {{ #vendor }}
                                <div>
                                    <a href="http://rtpslotgeng414.com/index/vendor/{{ vendor_name }}">
                                        <amp-img media="(max-width: 998px)" class="image-layout" src="{{ image_url_vendor }}" width="90" height="45" alt="pragmatic"></amp-img>
                                        <amp-img media="(min-width: 998px)" src="{{ image_url_vendor }}" width="130" height="63" alt="pragmatic"></amp-img>
                                    </a>
                                </div>
                                {{ /vendor }}
                            </div>
                        </section>
                    </div>
                </template>
            <div class="i-amphtml-fill-content i-amphtml-replaced-content"><div class="app-footer">
                        <section class="app-footer__partners">
                            <div class="grid-container">
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Pragmatic%20Play">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/pragmatic.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/pragmatic.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_pragmatic.png.69e22dab99.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Habanero">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/habanero.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/habanero.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_habanero.png.12c061c261.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/IDN%20Slot">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/indslot.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/indslot.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_indslot.png.111f71727f.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Pocket%20Game%20Soft">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/pgsoft.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/pgsoft.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_pgsoft.png.6524cd9af1.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Microgaming">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/mocrogaming.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/mocrogaming.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_mocrogaming.png.593bd9bb56.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Top%20Trend">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/toptrend.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/toptrend.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_toptrend.png.c1b2c23a3b.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Game%20Media%20Works">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/gmw.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/gmw.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_gmw.png.92895dcc45.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Limit%20City">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/nolimitcity.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/nolimitcity.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_nolimitcity.png.73c062560f.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Play%20Star">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/playstar.svg" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/playstar.svg" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_playstar.svg.cceca67e19.svg" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Slot%20Mania">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://wl2.loobygameshub.com/newasset/slot_mania_new.svg" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://wl2.loobygameshub.com/newasset/slot_mania_new.svg" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/newasset_slot_mania_new.svg.6f772fcbc7.svg" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Fat%20Panda">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/fatpanda.webp" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/fatpanda.webp" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_fatpanda.webp.b48b33add3.webp" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/Booming%20Games">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/booming.webp" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/booming.webp" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_booming.webp.e1d4620e62.webp" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                                <div>
                                    <a target="_top" href="http://rtpslotgeng414.com/index/vendor/5G%20Games">
                                        <amp-img alt="pragmatic" height="45" width="90" src="https://statics.hokibagus.club/rtpslot/vendor_logo/5g.png" class="image-layout i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-hidden-by-media-query i-amphtml-built" media="(max-width: 998px)" i-amphtml-ignore="" i-amphtml-layout="fixed" style="width: 90px; height: 45px;"></amp-img>
                                        <amp-img alt="pragmatic" height="63" width="130" src="https://statics.hokibagus.club/rtpslot/vendor_logo/5g.png" media="(min-width: 998px)" i-amphtml-ignore="" class="i-amphtml-element i-amphtml-layout-fixed i-amphtml-layout-size-defined i-amphtml-built i-amphtml-layout" i-amphtml-layout="fixed" style="width: 130px; height: 63px;"><img decoding="async" alt="pragmatic" src="assets/rtpslot_vendor_logo_5g.png.d08e76aa80.png" class="i-amphtml-fill-content i-amphtml-replaced-content"></amp-img>
                                    </a>
                                </div>
                            </div>
                        </section>
                    </div></div></amp-list>

            <amp-state id="allGames" src="/games.json" class="i-amphtml-element i-amphtml-layout-container i-amphtml-built" i-amphtml-layout="container" hidden="" aria-hidden="true"></amp-state>

            <input class="form-control" type="search" placeholder=" Cari Game" on="input-debounced:AMP.setState({
                filteredGames: allGames.items.filter(
                    game => event.value == ''
                    ? true
                    : game.game_name.toLowerCase().indexOf(event.value.toLowerCase()) > -1
                        ? game
                        : ''
                )
                })">
            <br>

            <amp-list
                width="auto"
                height="100"
                [height]="40 * filteredGames.length"
                layout="fixed-height"
                src="/games.json"
                [src]="filteredGames"
                class="game-list optimized-list"
                binding="no"
                load-more="auto"
                load-more-bookmark="next"
                max-items="50">
                <template type="amp-mustache">
                    <div class="card game-one-half-slot slots-games" data-game-id="{{ id }}">
                        <div class="card-content">
                            <a class="hover-btn" href="{{ play_url }}" target="_top" rel="noopener">
                                <div class="play-btn">PLAY NOW</div>
                            </a>
                            <a href="{{ play_url }}" target="_top" rel="noopener">
                                <amp-img
                                    class="img-zoom"
                                    src="{{ image_url }}"
                                    alt="{{ game_name }}"
                                    layout="responsive"
                                    width="200"
                                    height="150"
                                    loading="lazy"
                                    placeholder="blur">
                                    <div placeholder class="img-placeholder">
                                        <div class="loading-spinner"></div>
                                    </div>
                                </amp-img>
                            </a>
                            <div class="pbar">
                                <div class="pbar-bg {{ warna }}" style="width: {{ value }}%" role="progressbar" aria-valuenow="{{ value }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                                <div class="pbar-text">{{ value }}%</div>
                            </div>

                            <div class="my-icon d-block bg-dark p-2 mt-1 mt-md-1 mb-0 text-center lpola-1"
                                 [class]="pola ? 'fitur-yes' : 'fitur-no'"
                                 data-pola-available="{{ pola_rtp.available }}">
                                {{# pola_rtp.available }}
                                    {{# pola_rtp.patterns }}
                                    <div class="pola-pattern">
                                        {{ bet }}
                                        {{# icons }}
                                        <i class="material-symbols-rounded icon-{{ . }}">{{ . }}</i>
                                        {{/ icons }}
                                        {{ type }}
                                    </div>
                                    {{/ pola_rtp.patterns }}
                                {{/ pola_rtp.available }}
                                {{^ pola_rtp.available }}
                                    <div class="pola-unavailable">
                                        {{{ pola_rtp.message }}}
                                    </div>
                                {{/ pola_rtp.available }}
                            </div>
                        </div>
                    </div>
                </template>
                <div placeholder class="loading-placeholder">
                    <div class="loading-spinner-large"></div>
                    <p>Loading games...</p>
                </div>
                <div fallback class="error-fallback">
                    <p>Failed to load games. Please try again.</p>
                    <button on="tap:AMP.setState({reload: true})">Retry</button>
                </div>
            </amp-list>


        </div>
    </section>
            </div>
        </div>
    </main>

    <i class="material-symbols-rounded" style="color: #00ff00; font-size:0">check</i>

    <div class="container footer text-center mt-5">Copyright © 2018 gengtoto All rights reserved.
</div>



</body></html>