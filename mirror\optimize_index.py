#!/usr/bin/env python3
"""
Script untuk mengoptimasi index.html dengan memisahkan CSS dan JS
tanpa mengubah UI sama sekali
"""

import re
from pathlib import Path

def optimize_index_html():
    """Optimasi index.html dengan memisahkan CSS dan JS"""
    
    html_file = Path("index.html")
    if not html_file.exists():
        print("File index.html tidak ditemukan!")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_size = len(content)
    print(f"📄 Ukuran file asli: {original_size:,} bytes")
    
    # 1. Ekstrak CSS dari <style amp-custom="">
    css_pattern = r'<style amp-custom="">(.*?)</style>'
    css_match = re.search(css_pattern, content, re.DOTALL)
    
    if css_match:
        css_content = css_match.group(1).strip()
        
        # Simpan CSS ke file terpisah
        css_dir = Path("css")
        css_dir.mkdir(exist_ok=True)
        
        # Optimasi CSS
        optimized_css = optimize_css(css_content)
        
        with open(css_dir / "main.css", 'w', encoding='utf-8') as f:
            f.write(optimized_css)
        
        print(f"✅ CSS diekstrak: {len(css_content):,} → {len(optimized_css):,} bytes")
        
        # Ganti CSS inline dengan link eksternal
        content = re.sub(css_pattern, '<link rel="stylesheet" href="css/main.css">', content, flags=re.DOTALL)
    
    # 2. Ekstrak dan optimasi JavaScript inline (jika ada)
    js_content = extract_and_create_js()
    
    if js_content:
        # Tambahkan script eksternal sebelum closing body
        script_tag = '\n    <script src="js/main.js" defer></script>\n</body>'
        content = content.replace('</body>', script_tag)
        print(f"✅ JavaScript ditambahkan: {len(js_content):,} bytes")
    
    # 3. Optimasi HTML
    optimized_html = optimize_html(content)
    optimized_size = len(optimized_html)
    
    # Simpan HTML yang sudah dioptimasi
    output_file = Path("index_optimized_separated.html")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(optimized_html)
    
    # Hitung pengurangan ukuran
    reduction = ((original_size - optimized_size) / original_size) * 100
    
    print(f"\n📊 Hasil Optimasi:")
    print(f"   HTML Original: {original_size:,} bytes")
    print(f"   HTML Optimized: {optimized_size:,} bytes")
    print(f"   Pengurangan: {reduction:.1f}%")
    print(f"   File output: {output_file}")

def optimize_css(css_content):
    """Optimasi CSS dengan minifikasi"""
    
    # Remove comments
    css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
    
    # Remove extra whitespace
    css_content = re.sub(r'\s+', ' ', css_content)
    css_content = re.sub(r';\s*}', '}', css_content)
    css_content = re.sub(r'{\s*', '{', css_content)
    css_content = re.sub(r';\s*', ';', css_content)
    css_content = re.sub(r':\s*', ':', css_content)
    css_content = re.sub(r',\s*', ',', css_content)
    
    # Remove trailing semicolons
    css_content = re.sub(r';}', '}', css_content)
    
    return css_content.strip()

def optimize_html(html_content):
    """Optimasi HTML dengan minifikasi ringan"""
    
    # Remove extra whitespace between tags (hati-hati dengan content)
    html_content = re.sub(r'>\s+<', '><', html_content)
    
    # Remove extra whitespace in attributes
    html_content = re.sub(r'\s+', ' ', html_content)
    
    return html_content.strip()

def extract_and_create_js():
    """Buat JavaScript untuk menggantikan fungsi AMP yang diperlukan"""
    
    js_content = """
// JavaScript untuk menggantikan fungsi AMP
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Optimized index.html loaded');
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize sidebar
    initializeSidebar();
    
    // Initialize lazy loading
    initializeLazyLoading();
    
    // Initialize form handling
    initializeForms();
});

function initializeSearch() {
    const searchInput = document.querySelector('input[type="search"], .search');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterGames(searchTerm);
        });
    }
}

function filterGames(searchTerm) {
    const gameCards = document.querySelectorAll('.card');
    let visibleCount = 0;
    
    gameCards.forEach(card => {
        const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';
        const isVisible = gameName.includes(searchTerm);
        
        card.style.display = isVisible ? 'block' : 'none';
        if (isVisible) visibleCount++;
    });
    
    console.log(`🔍 Found ${visibleCount} games matching "${searchTerm}"`);
}

function initializeSidebar() {
    const sidebarToggle = document.querySelector('[on*="sidebar"]');
    const sidebar = document.querySelector('amp-sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('open');
        });
    }
}

function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('📝 Form submitted');
            // Handle form submission here
        });
    });
}

// Performance monitoring
window.addEventListener('load', function() {
    const loadTime = performance.now();
    console.log(`⚡ Page loaded in ${Math.round(loadTime)}ms`);
});
"""
    
    # Simpan JavaScript ke file terpisah
    js_dir = Path("js")
    js_dir.mkdir(exist_ok=True)
    
    # Optimasi JavaScript
    optimized_js = optimize_js(js_content)
    
    with open(js_dir / "main.js", 'w', encoding='utf-8') as f:
        f.write(optimized_js)
    
    return optimized_js

def optimize_js(js_content):
    """Optimasi JavaScript dengan minifikasi ringan"""
    
    # Remove single-line comments
    js_content = re.sub(r'//.*$', '', js_content, flags=re.MULTILINE)
    
    # Remove multi-line comments
    js_content = re.sub(r'/\*.*?\*/', '', js_content, flags=re.DOTALL)
    
    # Remove extra whitespace
    js_content = re.sub(r'\s+', ' ', js_content)
    js_content = re.sub(r';\s*', ';', js_content)
    js_content = re.sub(r'{\s*', '{', js_content)
    js_content = re.sub(r'}\s*', '}', js_content)
    js_content = re.sub(r',\s*', ',', js_content)
    
    return js_content.strip()

def create_build_script():
    """Buat script build untuk optimasi otomatis"""
    
    build_script = """#!/usr/bin/env python3
# Build script untuk optimasi index.html

import subprocess
import sys
from pathlib import Path

def main():
    print("🚀 Building optimized index.html...")
    
    # Run optimization
    result = subprocess.run([sys.executable, "optimize_index.py"], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Build successful!")
        print(result.stdout)
        
        # Create production directory
        prod_dir = Path("production")
        prod_dir.mkdir(exist_ok=True)
        
        # Copy optimized files
        import shutil
        shutil.copy("index_optimized_separated.html", prod_dir / "index.html")
        shutil.copytree("css", prod_dir / "css", dirs_exist_ok=True)
        shutil.copytree("js", prod_dir / "js", dirs_exist_ok=True)
        shutil.copytree("assets", prod_dir / "assets", dirs_exist_ok=True)
        shutil.copy("games.json", prod_dir / "games.json")
        
        print(f"📁 Production files copied to: {prod_dir}")
    else:
        print("❌ Build failed!")
        print(result.stderr)

if __name__ == "__main__":
    main()
"""
    
    with open("build.py", 'w', encoding='utf-8') as f:
        f.write(build_script)
    
    print("📄 Build script created: build.py")

def main():
    """Main function"""
    print("🔧 Mengoptimasi index.html dengan pemisahan CSS dan JS...")
    print("=" * 60)
    
    # Optimasi file utama
    optimize_index_html()
    
    # Buat build script
    create_build_script()
    
    print("\n✅ Optimasi selesai!")
    print("\n📁 File yang dihasilkan:")
    print("   - css/main.css (CSS terpisah dan diminifikasi)")
    print("   - js/main.js (JavaScript terpisah dan diminifikasi)")
    print("   - index_optimized_separated.html (HTML optimized)")
    print("   - build.py (Build script untuk production)")
    
    print("\n🚀 Untuk build production, jalankan:")
    print("   python build.py")

if __name__ == "__main__":
    main()
