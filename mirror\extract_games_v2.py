#!/usr/bin/env python3
"""
Script untuk mengekstrak semua data game dari HTML dan mengkonversinya ke JSON
Versi 2 - Lebih akurat
"""

import re
import json
from pathlib import Path
from urllib.parse import unquote

def extract_games_from_html():
    """Ekstrak semua data game dari file HTML"""
    
    # Baca file HTML
    html_file = Path("index.html")
    if not html_file.exists():
        print("File index.html tidak ditemukan!")
        return []
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    games = []
    game_id = 1
    
    # Cari bagian yang berisi hardcoded games (setelah template)
    start_marker = '<div role="list" class="i-amphtml-fill-content i-amphtml-replaced-content">'
    start_pos = content.find(start_marker)
    
    if start_pos == -1:
        print("Tidak menemukan marker awal hardcoded games!")
        return []
    
    # <PERSON>bil bagian setelah marker
    games_section = content[start_pos:]
    
    # Pattern untuk mencari setiap card game
    card_pattern = r'<div class="card game-one-half-slot slots-games"[^>]*role="listitem">(.*?)(?=<div class="card game-one-half-slot slots-games"|$)'
    
    # Cari semua card game
    cards = re.findall(card_pattern, games_section, re.DOTALL)
    
    print(f"Ditemukan {len(cards)} card games")
    
    for i, card_html in enumerate(cards):
        try:
            game_data = extract_game_data(card_html, game_id)
            if game_data and game_data['game_name'] != "Game " + str(game_id):
                games.append(game_data)
                game_id += 1
                if game_id <= 5:  # Debug: tampilkan 5 game pertama
                    print(f"Game {game_id-1}: {game_data['game_name']} - RTP: {game_data['value']}%")
        except Exception as e:
            print(f"Error processing card {i+1}: {e}")
            continue
    
    return games

def extract_game_data(card_html, game_id):
    """Ekstrak data dari satu card game"""
    
    # Ekstrak nama game dari alt attribute
    alt_matches = re.findall(r'alt="([^"]+)"', card_html)
    game_name = None
    
    for alt in alt_matches:
        # Skip template variables dan nama yang terlalu pendek
        if alt and "{{" not in alt and len(alt) > 2:
            game_name = alt
            break
    
    if not game_name:
        # Coba ekstrak dari URL gambar
        img_url_match = re.search(r'https://statics\.hokibagus\.club/rtpslot/[^/]+/([^"]+)\.webp', card_html)
        if img_url_match:
            # Decode URL dan bersihkan nama
            raw_name = unquote(img_url_match.group(1))
            game_name = raw_name.replace('%20', ' ').replace('%', '')
        else:
            game_name = f"Game {game_id}"
    
    # Ekstrak URL gambar lokal
    local_img_match = re.search(r'src="(assets/[^"]+)"[^>]*class="i-amphtml-fill-content', card_html)
    image_url = local_img_match.group(1) if local_img_match else ""
    
    # Ekstrak URL gambar asli
    original_img_match = re.search(r'src="(https://statics\.hokibagus\.club/[^"]+)"', card_html)
    original_image_url = original_img_match.group(1) if original_img_match else ""
    
    # Ekstrak nilai RTP dan warna
    value = 50
    warna = "yellow"
    
    # Pattern untuk RTP
    rtp_patterns = [
        r'style="width: (\d+)%"[^>]*class="pbar-bg ([^"]+)"',
        r'class="pbar-bg ([^"]+)"[^>]*style="width: (\d+)%"'
    ]
    
    for pattern in rtp_patterns:
        rtp_match = re.search(pattern, card_html)
        if rtp_match:
            if pattern.startswith('style'):
                value = int(rtp_match.group(1))
                warna = rtp_match.group(2).strip()
            else:
                warna = rtp_match.group(1).strip()
                value = int(rtp_match.group(2))
            break
    
    # Ekstrak URL play
    play_url_match = re.search(r'href="([^"]+)"[^>]*class="hover-btn"', card_html)
    play_url = play_url_match.group(1) if play_url_match else "https://geng33041.com/"
    
    # Ekstrak pola RTP
    pola_rtp = extract_pola_patterns(card_html)
    
    # Tentukan provider
    provider = determine_provider(original_image_url, image_url)
    
    return {
        "id": game_id,
        "game_name": game_name,
        "image_url": image_url,
        "original_image_url": original_image_url,
        "value": value,
        "warna": warna,
        "play_url": play_url,
        "provider": provider,
        "pola_rtp": pola_rtp
    }

def determine_provider(original_url, local_url):
    """Tentukan provider dari URL"""
    url_to_check = original_url or local_url
    url_lower = url_to_check.lower()
    
    if "pragmatic" in url_lower:
        return "pragmatic"
    elif "pgsoft" in url_lower:
        return "pgsoft"
    elif "habanero" in url_lower:
        return "habanero"
    elif "booming" in url_lower:
        return "booming"
    elif "microgaming" in url_lower or "mocrogaming" in url_lower:
        return "microgaming"
    elif "nolimitcity" in url_lower:
        return "nolimitcity"
    elif "playstar" in url_lower:
        return "playstar"
    elif "toptrend" in url_lower:
        return "toptrend"
    elif "5g" in url_lower:
        return "5g"
    elif "fatpanda" in url_lower:
        return "fatpanda"
    elif "gmw" in url_lower:
        return "gmw"
    elif "indslot" in url_lower:
        return "indslot"
    else:
        return "pragmatic"  # Default

def extract_pola_patterns(card_html):
    """Ekstrak pola RTP dari card HTML"""
    
    # Cek apakah ada pesan "Pola tidak tersedia"
    if "Pola tidak tersedia" in card_html:
        return {
            "available": False,
            "message": "Pola tidak tersedia!<br>Silahkan gunakan pola anda pribadi."
        }
    
    # Ekstrak semua span dengan pola
    span_pattern = r'<span id="([^"]*prov[^"]*)"[^>]*>([^<]*(?:<i[^>]*>[^<]*</i>[^<]*)*)</span>'
    span_matches = re.findall(span_pattern, card_html, re.DOTALL)
    
    patterns = []
    for span_id, span_content in span_matches:
        # Parse content span
        # Hapus semua tag HTML untuk mendapatkan text bersih
        clean_content = re.sub(r'<[^>]+>', ' ', span_content)
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()
        
        if not clean_content:
            continue
        
        # Ekstrak icons dari HTML asli
        icons = extract_icons_from_span_content(span_content)
        
        # Parse bet dan type dari clean content
        parts = clean_content.split()
        if parts:
            bet = parts[0]
            # Type adalah bagian setelah icons (biasanya "Auto" atau kosong)
            type_text = ""
            if len(parts) > 1:
                # Cari kata "Auto" atau "Manual"
                for part in parts[1:]:
                    if part in ["Auto", "Manual"]:
                        type_text = part
                        break
                    elif part.startswith("Manual"):
                        type_text = ""
                        bet = " ".join(parts[:2])  # "Manual X"
                        break
            
            patterns.append({
                "id": span_id,
                "bet": bet,
                "icons": icons,
                "type": type_text
            })
    
    return {
        "available": True,
        "patterns": patterns
    } if patterns else {
        "available": False,
        "message": "Pola tidak tersedia!<br>Silahkan gunakan pola anda pribadi."
    }

def extract_icons_from_span_content(span_content):
    """Ekstrak icons dari content span"""
    icon_pattern = r'<i[^>]*>([^<]+)</i>'
    icons = re.findall(icon_pattern, span_content)
    return icons

def main():
    """Main function"""
    print("Mengekstrak data game dari HTML...")
    
    games = extract_games_from_html()
    
    if not games:
        print("Tidak ada data game yang ditemukan!")
        return
    
    print(f"Berhasil mengekstrak {len(games)} game")
    
    # Simpan ke file JSON
    output_file = Path("games_complete.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(games, f, indent=2, ensure_ascii=False)
    
    print(f"Data game disimpan ke {output_file}")
    
    # Tampilkan statistik
    providers = {}
    for game in games:
        provider = game['provider']
        providers[provider] = providers.get(provider, 0) + 1
    
    print(f"\nStatistik provider:")
    for provider, count in providers.items():
        print(f"  {provider}: {count} games")

if __name__ == "__main__":
    main()
