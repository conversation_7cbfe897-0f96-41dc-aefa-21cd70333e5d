#!/usr/bin/env python3
# Build script untuk optimasi index.html

import subprocess
import sys
from pathlib import Path

def main():
    print("🚀 Building optimized index.html...")
    
    # Run optimization
    result = subprocess.run([sys.executable, "optimize_index.py"], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Build successful!")
        print(result.stdout)
        
        # Create production directory
        prod_dir = Path("production")
        prod_dir.mkdir(exist_ok=True)
        
        # Copy optimized files
        import shutil
        shutil.copy("index_optimized_separated.html", prod_dir / "index.html")
        shutil.copytree("css", prod_dir / "css", dirs_exist_ok=True)
        shutil.copytree("js", prod_dir / "js", dirs_exist_ok=True)
        shutil.copytree("assets", prod_dir / "assets", dirs_exist_ok=True)
        shutil.copy("games.json", prod_dir / "games.json")
        
        print(f"📁 Production files copied to: {prod_dir}")
    else:
        print("❌ Build failed!")
        print(result.stderr)

if __name__ == "__main__":
    main()
