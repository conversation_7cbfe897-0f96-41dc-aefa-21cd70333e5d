#!/usr/bin/env python3
"""
Build Script for Maximum Performance Optimization
Minifies CSS, JS, and optimizes assets
"""

import os
import re
import json
import gzip
import shutil
from pathlib import Path
import subprocess

class PerformanceBuilder:
    def __init__(self):
        self.root_dir = Path('.')
        self.dist_dir = Path('./dist')
        self.stats = {
            'original_sizes': {},
            'optimized_sizes': {},
            'compression_ratios': {}
        }
    
    def build(self):
        """Main build process"""
        print("🚀 Starting performance optimization build...")
        
        # Clean and create dist directory
        self.clean_dist()
        
        # Copy and optimize files
        self.optimize_html()
        self.optimize_css()
        self.optimize_js()
        self.optimize_json()
        self.copy_assets()
        self.generate_gzip()
        
        # Generate build report
        self.generate_report()
        
        print("✅ Build completed successfully!")
    
    def clean_dist(self):
        """Clean and create distribution directory"""
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        self.dist_dir.mkdir(exist_ok=True)
        (self.dist_dir / 'css').mkdir(exist_ok=True)
        (self.dist_dir / 'js').mkdir(exist_ok=True)
        (self.dist_dir / 'assets').mkdir(exist_ok=True)
    
    def optimize_html(self):
        """Optimize HTML files"""
        print("📄 Optimizing HTML...")
        
        html_files = ['index_optimized.html', 'comparison.html']
        
        for html_file in html_files:
            if Path(html_file).exists():
                content = Path(html_file).read_text(encoding='utf-8')
                original_size = len(content)
                
                # Minify HTML
                optimized_content = self.minify_html(content)
                optimized_size = len(optimized_content)
                
                # Write optimized file
                output_path = self.dist_dir / html_file
                output_path.write_text(optimized_content, encoding='utf-8')
                
                self.stats['original_sizes'][html_file] = original_size
                self.stats['optimized_sizes'][html_file] = optimized_size
                
                print(f"  ✓ {html_file}: {original_size} → {optimized_size} bytes ({self.get_compression_ratio(original_size, optimized_size)}% smaller)")
    
    def optimize_css(self):
        """Optimize CSS files"""
        print("🎨 Optimizing CSS...")
        
        css_files = ['css/critical.css', 'css/main.css']
        
        for css_file in css_files:
            if Path(css_file).exists():
                content = Path(css_file).read_text(encoding='utf-8')
                original_size = len(content)
                
                # Minify CSS
                optimized_content = self.minify_css(content)
                optimized_size = len(optimized_content)
                
                # Write optimized file
                output_path = self.dist_dir / css_file
                output_path.parent.mkdir(exist_ok=True)
                output_path.write_text(optimized_content, encoding='utf-8')
                
                self.stats['original_sizes'][css_file] = original_size
                self.stats['optimized_sizes'][css_file] = optimized_size
                
                print(f"  ✓ {css_file}: {original_size} → {optimized_size} bytes ({self.get_compression_ratio(original_size, optimized_size)}% smaller)")
    
    def optimize_js(self):
        """Optimize JavaScript files"""
        print("⚡ Optimizing JavaScript...")
        
        js_files = ['js/app.js', 'js/performance.js', 'sw.js']
        
        for js_file in js_files:
            if Path(js_file).exists():
                content = Path(js_file).read_text(encoding='utf-8')
                original_size = len(content)
                
                # Minify JavaScript
                optimized_content = self.minify_js(content)
                optimized_size = len(optimized_content)
                
                # Write optimized file
                if js_file == 'sw.js':
                    output_path = self.dist_dir / js_file
                else:
                    output_path = self.dist_dir / js_file
                    output_path.parent.mkdir(exist_ok=True)
                
                output_path.write_text(optimized_content, encoding='utf-8')
                
                self.stats['original_sizes'][js_file] = original_size
                self.stats['optimized_sizes'][js_file] = optimized_size
                
                print(f"  ✓ {js_file}: {original_size} → {optimized_size} bytes ({self.get_compression_ratio(original_size, optimized_size)}% smaller)")
    
    def optimize_json(self):
        """Optimize JSON files"""
        print("📊 Optimizing JSON...")
        
        json_files = ['games.json', 'manifest.json']
        
        for json_file in json_files:
            if Path(json_file).exists():
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                original_content = Path(json_file).read_text(encoding='utf-8')
                original_size = len(original_content)
                
                # Minify JSON
                optimized_content = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
                optimized_size = len(optimized_content)
                
                # Write optimized file
                output_path = self.dist_dir / json_file
                output_path.write_text(optimized_content, encoding='utf-8')
                
                self.stats['original_sizes'][json_file] = original_size
                self.stats['optimized_sizes'][json_file] = optimized_size
                
                print(f"  ✓ {json_file}: {original_size} → {optimized_size} bytes ({self.get_compression_ratio(original_size, optimized_size)}% smaller)")
    
    def copy_assets(self):
        """Copy asset files"""
        print("📁 Copying assets...")
        
        assets_dir = Path('assets')
        if assets_dir.exists():
            shutil.copytree(assets_dir, self.dist_dir / 'assets', dirs_exist_ok=True)
            print("  ✓ Assets copied")
    
    def generate_gzip(self):
        """Generate gzipped versions of files"""
        print("🗜️ Generating gzipped files...")
        
        for file_path in self.dist_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix in ['.html', '.css', '.js', '.json']:
                with open(file_path, 'rb') as f_in:
                    with gzip.open(f"{file_path}.gz", 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                original_size = file_path.stat().st_size
                gzipped_size = Path(f"{file_path}.gz").stat().st_size
                
                print(f"  ✓ {file_path.name}: {original_size} → {gzipped_size} bytes (gzipped)")
    
    def minify_html(self, content):
        """Minify HTML content"""
        # Remove comments
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'>\s+<', '><', content)
        
        # Remove whitespace around specific tags
        content = re.sub(r'\s*<\s*', '<', content)
        content = re.sub(r'\s*>\s*', '>', content)
        
        return content.strip()
    
    def minify_css(self, content):
        """Minify CSS content"""
        # Remove comments
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r';\s*}', '}', content)
        content = re.sub(r'{\s*', '{', content)
        content = re.sub(r';\s*', ';', content)
        content = re.sub(r':\s*', ':', content)
        content = re.sub(r',\s*', ',', content)
        
        # Remove trailing semicolons
        content = re.sub(r';}', '}', content)
        
        return content.strip()
    
    def minify_js(self, content):
        """Basic JavaScript minification"""
        # Remove single-line comments
        content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
        
        # Remove multi-line comments
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r';\s*', ';', content)
        content = re.sub(r'{\s*', '{', content)
        content = re.sub(r'}\s*', '}', content)
        content = re.sub(r',\s*', ',', content)
        
        return content.strip()
    
    def get_compression_ratio(self, original, optimized):
        """Calculate compression ratio"""
        if original == 0:
            return 0
        return round(((original - optimized) / original) * 100, 1)
    
    def generate_report(self):
        """Generate build optimization report"""
        print("\n📊 Build Optimization Report")
        print("=" * 50)
        
        total_original = sum(self.stats['original_sizes'].values())
        total_optimized = sum(self.stats['optimized_sizes'].values())
        total_savings = total_original - total_optimized
        total_ratio = self.get_compression_ratio(total_original, total_optimized)
        
        print(f"Total Original Size: {self.format_bytes(total_original)}")
        print(f"Total Optimized Size: {self.format_bytes(total_optimized)}")
        print(f"Total Savings: {self.format_bytes(total_savings)} ({total_ratio}%)")
        print()
        
        print("File Details:")
        for filename in self.stats['original_sizes']:
            original = self.stats['original_sizes'][filename]
            optimized = self.stats['optimized_sizes'][filename]
            ratio = self.get_compression_ratio(original, optimized)
            
            print(f"  {filename}:")
            print(f"    Original: {self.format_bytes(original)}")
            print(f"    Optimized: {self.format_bytes(optimized)}")
            print(f"    Savings: {ratio}%")
        
        # Save report to file
        report_data = {
            'timestamp': str(Path().resolve()),
            'total_original_size': total_original,
            'total_optimized_size': total_optimized,
            'total_savings_bytes': total_savings,
            'total_savings_percent': total_ratio,
            'files': {}
        }
        
        for filename in self.stats['original_sizes']:
            report_data['files'][filename] = {
                'original_size': self.stats['original_sizes'][filename],
                'optimized_size': self.stats['optimized_sizes'][filename],
                'savings_percent': self.get_compression_ratio(
                    self.stats['original_sizes'][filename],
                    self.stats['optimized_sizes'][filename]
                )
            }
        
        with open(self.dist_dir / 'build-report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {self.dist_dir}/build-report.json")
    
    def format_bytes(self, bytes_size):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB']:
            if bytes_size < 1024.0:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024.0
        return f"{bytes_size:.1f} GB"

def main():
    """Main function"""
    builder = PerformanceBuilder()
    builder.build()

if __name__ == "__main__":
    main()
