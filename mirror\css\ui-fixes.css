
/* UI Fixes untuk struktur yang diperbaiki */

/* Logo fixes */
.flex-item-center img {
    max-width: 100px;
    height: 33px;
    width: auto;
}

/* Sidebar fixes */
.side-bar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: #1a1a1a;
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.side-bar.open {
    right: 0;
}

/* <PERSON><PERSON> fixes */
.menu-side {
    display: block;
    padding: 15px 20px;
    color: #FAF0D7;
    text-decoration: none;
    border-bottom: 1px solid #333;
    transition: background-color 0.2s ease;
}

.menu-side:hover {
    background-color: #333;
}

/* Submenu fixes */
.submenu {
    display: none;
    background: #2a2a2a;
}

.submenu-toggle {
    cursor: pointer;
    position: relative;
}

.submenu-toggle:after {
    content: "▶";
    position: absolute;
    right: 20px;
    transition: transform 0.2s ease;
}

.submenu-toggle.active:after {
    transform: rotate(90deg);
}

.submenu-toggle.active + .submenu {
    display: block;
}

.submenu-back {
    background: #333;
    cursor: pointer;
}

/* But<PERSON> fixes */
.side {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .side-bar {
        width: 250px;
        right: -250px;
    }
    
    .flex-item-center img {
        max-width: 80px;
        height: 26px;
    }
}

/* Animation fixes */
.rtpslot {
    animation: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Loading fixes */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}


/* Additional fixes untuk elemen yang hilang */

/* Menu button fixes */
.side.btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #FAF0D7;
}

.side.btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

/* Sidebar container fixes */
.side-bar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: #1a1a1a;
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.3);
}

.side-bar.open {
    right: 0;
}

/* Button general fixes */
.btn {
    display: inline-block;
    padding: 6px 12px;
    touch-action: manipulation;
    cursor: pointer;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    font: 500 18.5px BebasNeue;
    color: #FAF0D7;
    text-shadow: 0 0 3px #000;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Search input fixes */
input[type="search"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
}

input[type="search"]:focus {
    outline: none;
    border-color: #FFF349;
    box-shadow: 0 0 5px rgba(255, 243, 73, 0.3);
}

/* Card fixes */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Image fixes */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .side-bar {
        width: 280px;
        right: -280px;
    }
    
    .flex-container-side {
        padding: 0 10px;
    }
    
    .btn {
        font-size: 16px;
        padding: 4px 8px;
    }
}

@media (max-width: 480px) {
    .side-bar {
        width: 250px;
        right: -250px;
    }
    
    .flex-container-side {
        padding: 0 5px;
    }
}
