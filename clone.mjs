// clone-ui.mjs
import { chromium } from "playwright";
import fs from "node:fs/promises";
import path from "node:path";
import crypto from "node:crypto";
import { JSDOM } from "jsdom";
import { lookup as mimeLookup, extension as mimeExt } from "mime-types";

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

function hashShort(input) {
  return crypto.createHash("sha1").update(input).digest("hex").slice(0, 10);
}
function safeName(u) {
  try {
    const url = new URL(u);
    const base = url.pathname.split("/").filter(Boolean).join("_") || "index";
    let ext = path.extname(url.pathname);
    if (!ext) {
      const qext = url.search.match(/(\.|ext=)(\w{2,4})/i)?.[2] || "";
      ext = qext ? "." + qext : "";
    }
    return `${base}.${hashShort(u)}${ext || ""}`;
  } catch {
    return `asset.${hashShort(String(u))}`;
  }
}
function ensureExtByMime(filename, mime) {
  let ext = path.extname(filename);
  if (!ext && mime) {
    const mext = mimeExt(mime);
    if (mext) filename += "." + mext;
  }
  return filename;
}
function toAbs(u, base) {
  try {
    return new URL(u, base).href;
  } catch {
    return u;
  }
}
function uniq(arr) {
  return [...new Set(arr.filter(Boolean))];
}

async function download(url, outDir, urlToFileMap) {
  if (urlToFileMap.has(url)) return urlToFileMap.get(url);
  const res = await fetch(url, { redirect: "follow" });
  if (!res.ok) throw new Error(`HTTP ${res.status} for ${url}`);
  const buf = Buffer.from(await res.arrayBuffer());
  const mime = res.headers.get("content-type")?.split(";")[0] ?? "";
  let file = safeName(url);
  file = ensureExtByMime(file, mime);
  const outPath = path.join(outDir, "assets", file);
  await fs.mkdir(path.dirname(outPath), { recursive: true });
  await fs.writeFile(outPath, buf);
  const rel = `assets/${file}`;
  urlToFileMap.set(url, rel);
  return rel;
}

function rewriteHtmlDom(dom, urlToFileMap, baseUrl) {
  const d = dom.window.document;

  // Utility untuk rewrite atribut URL
  const rewriteAttr = (sel, attr) => {
    d.querySelectorAll(sel).forEach((el) => {
      const val = el.getAttribute(attr);
      if (!val) return;
      const abs = toAbs(val, baseUrl);
      if (urlToFileMap.has(abs)) el.setAttribute(attr, urlToFileMap.get(abs));
      else if (/^https?:\/\//i.test(abs)) el.setAttribute(attr, abs); // biarkan remote kalau tidak diunduh
    });
  };

  // Matikan eksekusi JS
  d.querySelectorAll("script").forEach((s) => {
    s.setAttribute("type", "text/plain");
    s.removeAttribute("nonce");
    s.removeAttribute("integrity");
    s.removeAttribute("crossorigin");
    if (s.hasAttribute("src"))
      s.setAttribute("data-src-preserved", s.getAttribute("src"));
    s.removeAttribute("src");
  });

  // Rewrite umum
  rewriteAttr('link[rel~="stylesheet"][href]', "href");
  rewriteAttr("img[src]", "src");
  rewriteAttr("source[src]", "src");
  rewriteAttr("video[poster]", "poster");
  rewriteAttr("audio[src]", "src");
  rewriteAttr("iframe[src]", "src");
  rewriteAttr("link[rel~='icon'][href]", "href");
  rewriteAttr("use[xlink\\:href]", "xlink:href"); // svg <use>
  rewriteAttr("use[href]", "href");

  // Simplify srcset (ambil yang resolusi 1x/terkecil)
  d.querySelectorAll("img[srcset], source[srcset]").forEach((el) => {
    const ss = el.getAttribute("srcset") || "";
    const firstUrl = ss.split(",")[0]?.trim().split(/\s+/)[0];
    if (firstUrl) {
      const abs = toAbs(firstUrl, baseUrl);
      const mapped = urlToFileMap.get(abs) || abs;
      if (el.tagName === "SOURCE") el.setAttribute("srcset", mapped);
      else el.setAttribute("src", mapped);
    }
    el.removeAttribute("srcset");
    el.removeAttribute("sizes");
  });

  // Hapus SRI/CORS yg bisa mengganggu saat offline
  d.querySelectorAll("link,script").forEach((el) => {
    el.removeAttribute("integrity");
    el.removeAttribute("crossorigin");
    el.removeAttribute("referrerpolicy");
  });

  // Tambah base minimal (optional)
  const baseEl = d.createElement("base");
  baseEl.setAttribute("href", "./");
  const head = d.querySelector("head") || d.documentElement;
  head.prepend(baseEl);

  return dom.serialize();
}

function collectDomAssets(dom, baseUrl) {
  const d = dom.window.document;
  const urls = [];

  const pushAttr = (sel, attr) =>
    d.querySelectorAll(sel).forEach((el) => {
      const val = el.getAttribute(attr);
      if (val) urls.push(toAbs(val, baseUrl));
    });

  pushAttr('link[rel~="stylesheet"][href]', "href");
  pushAttr("script[src]", "src");
  pushAttr("img[src]", "src");
  pushAttr("source[src]", "src");
  pushAttr("video[poster]", "poster");
  pushAttr("audio[src]", "src");
  pushAttr("link[rel~='icon'][href]", "href");
  pushAttr("use[xlink\\:href]", "xlink:href");
  pushAttr("use[href]", "href");

  // Dari style tag inline: cari url(...)
  dom.window.document.querySelectorAll("style").forEach((styleEl) => {
    const css = styleEl.textContent || "";
    const matches = css.match(/url\(([^)]+)\)/g) || [];
    matches.forEach((m) => {
      const raw = m
        .slice(4, -1)
        .trim()
        .replace(/^['"]|['"]$/g, "");
      urls.push(toAbs(raw, baseUrl));
    });
  });

  return uniq(urls);
}

function collectCssUrls(cssText, baseUrl) {
  const urls = [];
  const re = /url\(([^)]+)\)/g;
  let m;
  while ((m = re.exec(cssText))) {
    let raw = (m[1] || "").trim().replace(/^['"]|['"]$/g, "");
    if (!raw || raw.startsWith("data:")) continue;
    urls.push(toAbs(raw, baseUrl));
  }
  return uniq(urls);
}

function rewriteCss(cssText, replacer) {
  return cssText.replace(/url\(([^)]+)\)/g, (full, p1) => {
    let raw = (p1 || "").trim().replace(/^['"]|['"]$/g, "");
    if (!raw || raw.startsWith("data:")) return full;
    const newUrl = replacer(raw);
    return `url("${newUrl}")`;
  });
}

async function main() {
  const url = process.argv[2];
  const outDir = process.argv[3] || "mirror";
  if (!url) {
    console.error("Usage: node clone-ui.mjs <URL> [outDir]");
    process.exit(1);
  }

  await fs.mkdir(outDir, { recursive: true });
  await fs.mkdir(path.join(outDir, "assets"), { recursive: true });

  // 1) Render halaman
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage({
    userAgent:
      "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0 Safari/537.36",
  });

  // (opsional) kecilkan noise: block media berat
  await page.route("**/*", (route) => {
    const req = route.request();
    const type = req.resourceType();
    const u = req.url();
    if (/\.(mp4|webm|m4v|avi|mov)(\?|$)/i.test(u)) return route.abort();
    if (type === "media") return route.abort();
    route.continue();
  });

  await page.goto(url, { waitUntil: "networkidle", timeout: 60000 });
  // beri waktu untuk animasi/loader
  await sleep(500);

  // 2) Ambil HTML post-render
  const htmlRendered = await page.content();
  await browser.close();

  // 3) Parse DOM, kumpulkan asset DOM
  const dom = new JSDOM(htmlRendered);
  const baseUrl = url;
  const urlToFileMap = new Map();

  let domAssets = collectDomAssets(dom, baseUrl);

  // 4) Download semua stylesheet lebih dulu → parse url() di CSS → download turunan
  const stylesheetUrls = domAssets.filter((u) => /\.css(\?|$)/i.test(u));
  for (const cssUrl of stylesheetUrls) {
    try {
      const res = await fetch(cssUrl);
      if (!res.ok) continue;
      const css = await res.text();
      // ambil url() di dalam CSS
      const subAssets = collectCssUrls(css, cssUrl);
      // download sub-asset (font, bg image, dll)
      for (const sa of subAssets) {
        try {
          await download(sa, outDir, urlToFileMap);
        } catch {}
      }
      // setelah sub-asset diunduh, rewrite CSS → lokal
      const rewritten = rewriteCss(css, (raw) => {
        const abs = toAbs(raw, cssUrl);
        return urlToFileMap.get(abs) || abs;
      });
      // simpan CSS ke assets & map
      const localCssPath = await download(
        "data:text/css;base64," + Buffer.from(rewritten).toString("base64"),
        outDir,
        urlToFileMap
      );
      urlToFileMap.set(cssUrl, localCssPath);
    } catch {}
  }

  // 5) Download asset DOM lainnya (img/js/svg/icon/etc.)
  //    (JS tetap diunduh, tapi nanti dinonaktifkan agar tak dieksekusi)
  for (const a of domAssets) {
    if (urlToFileMap.has(a)) continue; // CSS sudah ditangani dan di-rewrite
    try {
      await download(a, outDir, urlToFileMap);
    } catch {}
  }

  // 6) Rewrite HTML → referensi lokal & matikan JS
  const finalHtml = rewriteHtmlDom(dom, urlToFileMap, baseUrl);

  // 7) Simpan HTML
  const outHtml = path.join(outDir, "index.html");
  await fs.writeFile(outHtml, finalHtml, "utf8");

  console.log(`✅ Selesai.
- HTML: ${outHtml}
- Asset: ${path.join(outDir, "assets")}
Buka secara lokal dengan server statis, mis.:
  npx http-server ${outDir} -p 8080
atau
  python3 -m http.server --directory ${outDir} 8080
`);
}

main().catch((err) => {
  console.error("❌ Error:", err);
  process.exit(1);
});
