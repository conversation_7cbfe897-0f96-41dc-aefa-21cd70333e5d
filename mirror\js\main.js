document.addEventListener('DOMContentLoaded',function() {console.log('🚀 Optimized index.html loaded');initializeSearch();initializeSidebar();initializeLazyLoading();initializeForms();});function initializeSearch() {const searchInput = document.querySelector('input[type="search"],.search');if (searchInput) {searchInput.addEventListener('input',function(e) {const searchTerm = e.target.value.toLowerCase();filterGames(searchTerm);});}}function filterGames(searchTerm) {const gameCards = document.querySelectorAll('.card');let visibleCount = 0;gameCards.forEach(card => {const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';const isVisible = gameName.includes(searchTerm);card.style.display = isVisible ? 'block' : 'none';if (isVisible) visibleCount++;});console.log(`🔍 Found ${visibleCount}games matching "${searchTerm}"`);}function initializeSidebar() {const sidebarToggle = document.querySelector('[on*="sidebar"]');const sidebar = document.querySelector('amp-sidebar');if (sidebarToggle && sidebar) {sidebarToggle.addEventListener('click',function(e) {e.preventDefault();sidebar.classList.toggle('open');});}}function initializeLazyLoading() {if ('IntersectionObserver' in window) {const imageObserver = new IntersectionObserver((entries,observer) => {entries.forEach(entry => {if (entry.isIntersecting) {const img = entry.target;if (img.dataset.src) {img.src = img.dataset.src;img.removeAttribute('data-src');observer.unobserve(img);}}});});document.querySelectorAll('img[data-src]').forEach(img => {imageObserver.observe(img);});}}function initializeForms() {const forms = document.querySelectorAll('form');forms.forEach(form => {form.addEventListener('submit',function(e) {e.preventDefault();console.log('📝 Form submitted');});});}window.addEventListener('load',function() {const loadTime = performance.now();console.log(`⚡ Page loaded in ${Math.round(loadTime)}ms`);});