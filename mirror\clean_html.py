#!/usr/bin/env python3
"""
Script untuk membersihkan HTML dari hardcoded game cards
"""

import re
from pathlib import Path

def clean_html():
    """Bersihkan HTML dari hardcoded game cards"""
    
    # Baca file HTML
    html_file = Path("index.html")
    if not html_file.exists():
        print("File index.html tidak ditemukan!")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Ukuran file asli: {len(content)} karakter")
    
    # Cari posisi awal dan akhir hardcoded cards
    start_marker = '</amp-list>\n\n                    </div><div class="card game-one-half-slot slots-games"'
    end_marker = '                    </div></div></amp-list>'
    
    start_pos = content.find(start_marker)
    end_pos = content.find(end_marker, start_pos)  # Cari setelah start_pos

    if start_pos == -1 or end_pos == -1:
        print("Tidak menemukan marker hardcoded cards!")
        print(f"Start marker found: {start_pos != -1}")
        print(f"End marker found: {end_pos != -1}")
        return

    print(f"Hardcoded cards ditemukan dari posisi {start_pos} sampai {end_pos}")
    
    # Hapus hardcoded cards
    # Ambil bagian sebelum hardcoded cards
    before_cards = content[:start_pos + len('</amp-list>')]
    
    # Ambil bagian setelah hardcoded cards
    after_cards = content[end_pos + len('                    </div></div></amp-list>'):]
    
    # Gabungkan
    cleaned_content = before_cards + '\n\n' + after_cards
    
    print(f"Ukuran file setelah dibersihkan: {len(cleaned_content)} karakter")
    print(f"Berhasil menghapus {len(content) - len(cleaned_content)} karakter")
    
    # Simpan file yang sudah dibersihkan
    backup_file = Path("index_backup.html")
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Backup disimpan ke {backup_file}")
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print(f"File HTML berhasil dibersihkan!")

def main():
    """Main function"""
    print("Membersihkan HTML dari hardcoded game cards...")
    clean_html()

if __name__ == "__main__":
    main()
