class GameApp {constructor() {this.games = [];this.filteredGames = [];this.isLoading = false;this.searchTimeout = null;this.searchInput = document.getElementById('searchInput');this.gamesGrid = document.getElementById('gamesGrid');this.loading = document.getElementById('loading');this.noResults = document.getElementById('noResults');this.performanceMetrics = {loadStart: performance.now(),gamesLoaded: null,firstRender: null };this.init();}async init() {try {this.showLoading();await this.loadGames();this.setupEventListeners();this.renderGames();this.setupOptimizations();this.trackPerformance();}catch (error) {console.error('App initialization failed:',error);this.showError('Failed to initialize app. Please refresh the page.');}}async loadGames() {try {const controller = new AbortController();const timeoutId = setTimeout(() => controller.abort(),10000);const response = await fetch('games.json',{signal: controller.signal,cache: 'force-cache' });clearTimeout(timeoutId);if (!response.ok) {throw new Error(`HTTP ${response.status}: ${response.statusText}`);}this.games = await response.json();this.filteredGames = [...this.games];this.performanceMetrics.gamesLoaded = performance.now();console.log(`✅ Loaded ${this.games.length}games in ${Math.round(this.performanceMetrics.gamesLoaded - this.performanceMetrics.loadStart)}ms`);}catch (error) {if (error.name === 'AbortError') {throw new Error('Request timeout - please check your connection');}throw error;}}setupEventListeners() {this.searchInput.addEventListener('input',(e) => {clearTimeout(this.searchTimeout);this.searchTimeout = setTimeout(() => {this.filterGames(e.target.value);},300);});this.searchInput.addEventListener('keydown',(e) => {if (e.key === 'Escape') {e.target.value = '';this.filterGames('');}});this.setupLazyLoading();this.setupVisibilityOptimization();}filterGames(searchTerm) {const term = searchTerm.toLowerCase().trim();if (!term) {this.filteredGames = [...this.games];}else {this.filteredGames = this.games.filter(game => {return game.game_name.toLowerCase().includes(term) || game.provider.toLowerCase().includes(term);});}this.renderGames();}renderGames() {this.hideLoading();if (this.filteredGames.length === 0) {this.showNoResults();return;}this.hideNoResults();this.gamesGrid.classList.remove('hidden');const fragment = document.createDocumentFragment();requestAnimationFrame(() => {this.gamesGrid.innerHTML = '';this.filteredGames.forEach((game,index) => {const cardElement = this.createGameCard(game,index);fragment.appendChild(cardElement);});this.gamesGrid.appendChild(fragment);if (!this.performanceMetrics.firstRender) {this.performanceMetrics.firstRender = performance.now();}this.animateCards();});}createGameCard(game,index) {const card = document.createElement('div');card.className = 'game-card';card.dataset.gameId = game.id;card.dataset.index = index;card.innerHTML = ` <div class="game-image"> <img data-src="${game.image_url}" alt="${this.escapeHtml(game.game_name)}" class="lazy-image" loading="lazy"> <div class="image-placeholder">🎰</div> <a href="${game.play_url}" class="play-btn" target="_blank" rel="noopener"> ▶ PLAY NOW </a> </div> <div class="game-content"> <h3 class="game-title">${this.escapeHtml(game.game_name)}</h3> <div class="rtp-container"> <div class="rtp-bar"> <div class="rtp-fill ${game.warna}" style="width: 0%" data-width="${game.value}%"></div> </div> <div class="rtp-text">${game.value}% RTP</div> </div> <div class="pola-section"> ${this.createPolaContent(game.pola_rtp)}</div> </div> `;return card;}createPolaContent(polaRtp) {if (!polaRtp.available) {return `<div class="pola-unavailable">${polaRtp.message}</div>`;}return polaRtp.patterns.map(pattern => {const icons = pattern.icons.map(icon => `<span class="material-symbols-rounded icon-${icon}">${icon}</span>` ).join('');return ` <div class="pola-pattern"> <span>${this.escapeHtml(pattern.bet)}</span> <div class="pola-icons">${icons}</div> <span>${this.escapeHtml(pattern.type)}</span> </div> `;}).join('');}animateCards() {const cards = this.gamesGrid.querySelectorAll('.game-card');const animationObserver = new IntersectionObserver((entries) => {entries.forEach((entry,index) => {if (entry.isIntersecting) {setTimeout(() => {entry.target.classList.add('fade-in');const rtpFill = entry.target.querySelector('.rtp-fill');if (rtpFill) {setTimeout(() => {rtpFill.style.width = rtpFill.dataset.width;},200);}},index * 50);animationObserver.unobserve(entry.target);}});},{threshold: 0.1 });cards.forEach(card => animationObserver.observe(card));}setupLazyLoading() {const imageObserver = new IntersectionObserver((entries) => {entries.forEach(entry => {if (entry.isIntersecting) {const img = entry.target;const placeholder = img.nextElementSibling;img.onload = () => {img.classList.add('loaded');if (placeholder) placeholder.style.display = 'none';};img.onerror = () => {img.style.display = 'none';if (placeholder) {placeholder.textContent = '❌';placeholder.style.color = '#f44336';}};img.src = img.dataset.src;img.classList.remove('lazy-image');imageObserver.unobserve(img);}});},{threshold: 0.1,rootMargin: '50px' });const originalRenderGames = this.renderGames.bind(this);this.renderGames = function() {originalRenderGames();setTimeout(() => {document.querySelectorAll('.lazy-image').forEach(img => {imageObserver.observe(img);});},100);};}setupOptimizations() {this.preloadCriticalResources();this.setupMemoryManagement();this.setupErrorHandling();}preloadCriticalResources() {const criticalGames = this.games.slice(0,6);criticalGames.forEach(game => {const link = document.createElement('link');link.rel = 'preload';link.as = 'image';link.href = game.image_url;document.head.appendChild(link);});}setupMemoryManagement() {window.addEventListener('beforeunload',() => {clearTimeout(this.searchTimeout);});let scrollTimeout;window.addEventListener('scroll',() => {clearTimeout(scrollTimeout);scrollTimeout = setTimeout(() => {this.cleanupOffscreenImages();},250);},{passive: true });}cleanupOffscreenImages() {const cards = document.querySelectorAll('.game-card');const viewportHeight = window.innerHeight;cards.forEach(card => {const rect = card.getBoundingClientRect();const isOffscreen = rect.bottom < -viewportHeight || rect.top > viewportHeight * 2;if (isOffscreen) {const img = card.querySelector('img');if (img && img.src && img.src !== img.dataset.src) {img.src = '';img.classList.add('lazy-image');img.classList.remove('loaded');}}});}setupVisibilityOptimization() {document.addEventListener('visibilitychange',() => {if (document.hidden) {clearTimeout(this.searchTimeout);}});}setupErrorHandling() {window.addEventListener('error',(event) => {console.error('Global error:',event.error);});window.addEventListener('unhandledrejection',(event) => {console.error('Unhandled promise rejection:',event.reason);event.preventDefault();});}trackPerformance() {if ('web-vital' in window) {console.log('Performance tracking enabled');}const metrics = {loadTime: this.performanceMetrics.gamesLoaded - this.performanceMetrics.loadStart,renderTime: this.performanceMetrics.firstRender - this.performanceMetrics.gamesLoaded,totalGames: this.games.length };console.log('📊 Performance Metrics:',metrics);}escapeHtml(text) {const div = document.createElement('div');div.textContent = text;return div.innerHTML;}showLoading() {this.loading.classList.remove('hidden');this.gamesGrid.classList.add('hidden');this.noResults.classList.add('hidden');}hideLoading() {this.loading.classList.add('hidden');}showNoResults() {this.noResults.classList.remove('hidden');this.gamesGrid.classList.add('hidden');}hideNoResults() {this.noResults.classList.add('hidden');}showError(message) {this.loading.innerHTML = ` <div style="color: #f44336;text-align: center;"> <div style="font-size: 2rem;margin-bottom: 1rem;">❌</div> <p>${message}</p> <button onclick="location.reload()" style="margin-top: 1rem;padding: 0.5rem 1rem;background: #f44336;color: white;border: none;border-radius: 4px;cursor: pointer;"> Refresh Page </button> </div> `;this.loading.classList.remove('hidden');}}let gameApp;function initializeApp() {try {gameApp = new GameApp();}catch (error) {console.error('Failed to initialize app:',error);document.getElementById('loading').innerHTML = ` <div style="color: #f44336;"> <p>❌ Failed to load application</p> <button onclick="location.reload()">Retry</button> </div> `;}}if (document.readyState === 'loading') {document.addEventListener('DOMContentLoaded',initializeApp);}else {initializeApp();}window.gameApp = gameApp;