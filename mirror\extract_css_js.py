#!/usr/bin/env python3
"""
Script untuk mengekstrak CSS dan JS dari index.html tanpa mengubah UI
"""

import re
from pathlib import Path

def extract_css_from_html():
    """Ekstrak semua CSS dari index.html"""
    
    html_file = Path("index.html")
    if not html_file.exists():
        print("File index.html tidak ditemukan!")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ekstrak CSS dari <style amp-custom="">
    css_pattern = r'<style amp-custom="">(.*?)</style>'
    css_match = re.search(css_pattern, content, re.DOTALL)
    
    if css_match:
        css_content = css_match.group(1).strip()
        
        # Simpan CSS ke file terpisah
        css_dir = Path("css")
        css_dir.mkdir(exist_ok=True)
        
        with open(css_dir / "styles.css", 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        print(f"✅ CSS berhasil diekstrak: {len(css_content)} karakter")
        print(f"📁 Disimpan ke: css/styles.css")
        
        return css_content
    else:
        print("❌ Tidak menemukan CSS dalam file HTML")
        return None

def extract_js_from_html():
    """Ekstrak JavaScript dari index.html"""

    html_file = Path("index.html")
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()

    js_content = []

    # Ekstrak inline scripts (non-AMP)
    script_pattern = r'<script(?![^>]*(?:amp-|cdn\.ampproject\.org|async|custom-element))[^>]*>(.*?)</script>'
    script_matches = re.findall(script_pattern, content, re.DOTALL)

    for script in script_matches:
        script = script.strip()
        if script and not script.startswith('//') and len(script) > 10:
            js_content.append(script)

    # Ekstrak event handlers
    event_patterns = [
        r'on\w+="([^"]*)"',
        r'onclick="([^"]*)"',
        r'onload="([^"]*)"',
        r'onchange="([^"]*)"'
    ]

    for pattern in event_patterns:
        event_matches = re.findall(pattern, content)
        for event in event_matches:
            if event.strip() and len(event.strip()) > 5:
                js_content.append(f"// Event handler: {event.strip()}")

    # Ekstrak AMP state dan bind expressions
    amp_state_pattern = r'<amp-state[^>]*>(.*?)</amp-state>'
    amp_state_matches = re.findall(amp_state_pattern, content, re.DOTALL)

    for state in amp_state_matches:
        if state.strip():
            js_content.append(f"// AMP State: {state.strip()}")

    # Tambahkan JavaScript untuk menggantikan fungsi AMP
    replacement_js = """
// Replacement JavaScript for AMP functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search functionality
    const searchInput = document.querySelector('input[type="search"], .search');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterGames(searchTerm);
        });
    }

    // Initialize sidebar toggle
    const sidebarToggle = document.querySelector('[on*="sidebar"]');
    const sidebar = document.querySelector('amp-sidebar, .sidebar');
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }

    // Initialize form submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            // Handle form submission
            console.log('Form submitted');
        });
    });
});

// Game filtering function
function filterGames(searchTerm) {
    const gameCards = document.querySelectorAll('.card');
    gameCards.forEach(card => {
        const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';
        if (gameName.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initLazyLoading);
} else {
    initLazyLoading();
}
"""

    js_content.append(replacement_js)

    if js_content:
        # Gabungkan semua JavaScript
        combined_js = '\n\n'.join(js_content)

        # Simpan JS ke file terpisah
        js_dir = Path("js")
        js_dir.mkdir(exist_ok=True)

        with open(js_dir / "scripts.js", 'w', encoding='utf-8') as f:
            f.write(combined_js)

        print(f"✅ JavaScript berhasil diekstrak: {len(combined_js)} karakter")
        print(f"📁 Disimpan ke: js/scripts.js")

        return combined_js
    else:
        print("ℹ️ Tidak ada JavaScript yang ditemukan")
        return None

def create_optimized_html():
    """Buat versi HTML yang sudah dioptimasi dengan CSS dan JS terpisah"""
    
    html_file = Path("index.html")
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Hapus CSS inline dan ganti dengan link eksternal
    css_pattern = r'<style amp-custom="">(.*?)</style>'
    content = re.sub(css_pattern, '<link rel="stylesheet" href="css/styles.css">', content, flags=re.DOTALL)
    
    # Hapus JavaScript inline dan tambahkan script eksternal
    script_pattern = r'<script[^>]*>(.*?)</script>'
    
    # Simpan script AMP yang penting
    amp_scripts = []
    for match in re.finditer(script_pattern, content, re.DOTALL):
        script_tag = match.group(0)
        if ('amp-' in script_tag or 'cdn.ampproject.org' in script_tag or 
            'async' in script_tag or 'custom-element' in script_tag):
            amp_scripts.append(script_tag)
    
    # Hapus semua script inline kecuali AMP scripts
    content = re.sub(r'<script(?![^>]*(?:amp-|cdn\.ampproject\.org|async|custom-element))[^>]*>.*?</script>', '', content, flags=re.DOTALL)
    
    # Hapus event handlers inline
    content = re.sub(r'\s+on\w+="[^"]*"', '', content)
    
    # Tambahkan script eksternal sebelum closing body
    script_tag = '\n    <script src="js/scripts.js" defer></script>\n</body>'
    content = content.replace('</body>', script_tag)
    
    # Simpan HTML yang sudah dioptimasi
    optimized_file = Path("index_separated.html")
    with open(optimized_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ HTML optimized berhasil dibuat: {optimized_file}")
    
    # Hitung pengurangan ukuran
    original_size = len(Path("index.html").read_text(encoding='utf-8'))
    optimized_size = len(content)
    reduction = ((original_size - optimized_size) / original_size) * 100
    
    print(f"📊 Ukuran file:")
    print(f"   Original: {original_size:,} bytes")
    print(f"   Optimized: {optimized_size:,} bytes")
    print(f"   Reduction: {reduction:.1f}%")

def optimize_css():
    """Optimasi CSS yang sudah diekstrak"""
    
    css_file = Path("css/styles.css")
    if not css_file.exists():
        return
    
    with open(css_file, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    original_size = len(css_content)
    
    # Basic CSS optimization
    # Remove comments
    css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
    
    # Remove extra whitespace
    css_content = re.sub(r'\s+', ' ', css_content)
    css_content = re.sub(r';\s*}', '}', css_content)
    css_content = re.sub(r'{\s*', '{', css_content)
    css_content = re.sub(r';\s*', ';', css_content)
    css_content = re.sub(r':\s*', ':', css_content)
    css_content = re.sub(r',\s*', ',', css_content)
    
    # Remove trailing semicolons
    css_content = re.sub(r';}', '}', css_content)
    
    optimized_size = len(css_content)
    reduction = ((original_size - optimized_size) / original_size) * 100
    
    # Simpan CSS yang sudah dioptimasi
    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(css_content.strip())
    
    print(f"🎨 CSS optimized:")
    print(f"   Original: {original_size:,} bytes")
    print(f"   Optimized: {optimized_size:,} bytes")
    print(f"   Reduction: {reduction:.1f}%")

def optimize_js():
    """Optimasi JavaScript yang sudah diekstrak"""
    
    js_file = Path("js/scripts.js")
    if not js_file.exists():
        return
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    original_size = len(js_content)
    
    # Basic JS optimization
    # Remove single-line comments
    js_content = re.sub(r'//.*$', '', js_content, flags=re.MULTILINE)
    
    # Remove multi-line comments
    js_content = re.sub(r'/\*.*?\*/', '', js_content, flags=re.DOTALL)
    
    # Remove extra whitespace
    js_content = re.sub(r'\s+', ' ', js_content)
    js_content = re.sub(r';\s*', ';', js_content)
    js_content = re.sub(r'{\s*', '{', js_content)
    js_content = re.sub(r'}\s*', '}', js_content)
    js_content = re.sub(r',\s*', ',', js_content)
    
    optimized_size = len(js_content)
    reduction = ((original_size - optimized_size) / original_size) * 100 if original_size > 0 else 0
    
    # Simpan JS yang sudah dioptimasi
    with open(js_file, 'w', encoding='utf-8') as f:
        f.write(js_content.strip())
    
    print(f"⚡ JavaScript optimized:")
    print(f"   Original: {original_size:,} bytes")
    print(f"   Optimized: {optimized_size:,} bytes")
    print(f"   Reduction: {reduction:.1f}%")

def main():
    """Main function"""
    print("🚀 Memisahkan CSS dan JS dari index.html...")
    print("=" * 50)
    
    # Ekstrak CSS dan JS
    extract_css_from_html()
    extract_js_from_html()
    
    print("\n🔧 Mengoptimasi file yang diekstrak...")
    print("=" * 50)
    
    # Optimasi file yang diekstrak
    optimize_css()
    optimize_js()
    
    print("\n📄 Membuat HTML yang dioptimasi...")
    print("=" * 50)
    
    # Buat HTML yang sudah dioptimasi
    create_optimized_html()
    
    print("\n✅ Proses selesai!")
    print("📁 File yang dihasilkan:")
    print("   - css/styles.css (CSS terpisah)")
    print("   - js/scripts.js (JavaScript terpisah)")
    print("   - index_separated.html (HTML optimized)")

if __name__ == "__main__":
    main()
