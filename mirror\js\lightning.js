
// Lightning Fast JavaScript - Modern Setup
class LightningApp {
    constructor() {
        this.games = [];
        this.filteredGames = [];
        this.isLoading = false;
        
        this.init();
    }
    
    async init() {
        console.log('⚡ Lightning App initialized');
        
        // Wait for DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    async setup() {
        // Initialize all features in parallel
        await Promise.all([
            this.initializeSearch(),
            this.initializeSidebar(),
            this.initializeLazyLoading(),
            this.loadGames(),
            this.initializeServiceWorker()
        ]);
        
        console.log('🚀 All features initialized');
    }
    
    async initializeSearch() {
        const searchInput = document.querySelector('input[type="search"]');
        if (searchInput) {
            // Use passive listeners for better performance
            searchInput.addEventListener('input', this.debounce((e) => {
                this.filterGames(e.target.value.toLowerCase());
            }, 150), { passive: true });
            
            console.log('🔍 Search initialized');
        }
    }
    
    filterGames(searchTerm) {
        const gameCards = document.querySelectorAll('.card');
        let visibleCount = 0;
        
        // Use requestAnimationFrame for smooth filtering
        requestAnimationFrame(() => {
            gameCards.forEach(card => {
                const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';
                const gameText = card.textContent?.toLowerCase() || '';
                
                const isVisible = !searchTerm || 
                                 gameName.includes(searchTerm) || 
                                 gameText.includes(searchTerm);
                
                card.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });
            
            console.log(`🎮 ${visibleCount} games visible`);
        });
    }
    
    initializeSidebar() {
        const toggle = document.querySelector('.side, [on*="sidebar"]');
        const sidebar = document.querySelector('#sidebar1, .side-bar');
        
        if (toggle && sidebar) {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                sidebar.classList.toggle('open');
                
                // Use CSS transforms for hardware acceleration
                const isOpen = sidebar.classList.contains('open');
                sidebar.style.transform = isOpen ? 'translateX(0)' : 'translateX(100%)';
            }, { passive: false });
            
            console.log('📱 Sidebar initialized');
        }
    }
    
    initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px',
                threshold: 0.1
            });
            
            // Observe all images
            document.querySelectorAll('img[data-src], img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
            
            console.log('🖼️ Lazy loading initialized');
        }
    }
    
    async loadGames() {
        try {
            // Load games data
            const response = await fetch('/games.json');
            if (response.ok) {
                const data = await response.json();
                this.games = data.items || [];
                this.filteredGames = [...this.games];
                
                console.log(`🎮 Loaded ${this.games.length} games`);
                this.enhanceGameCards();
            }
        } catch (error) {
            console.error('❌ Failed to load games:', error);
        }
    }
    
    enhanceGameCards() {
        const cards = document.querySelectorAll('.card');
        
        cards.forEach(card => {
            // Add smooth hover effects
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px) scale(1.02)';
                card.style.transition = 'transform 0.2s ease';
            }, { passive: true });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            }, { passive: true });
        });
        
        console.log(`✨ Enhanced ${cards.length} game cards`);
    }
    
    async initializeServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/sw.js');
                console.log('🔧 Service Worker registered');
            } catch (error) {
                console.log('❌ Service Worker registration failed:', error);
            }
        }
    }
    
    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize app
const app = new LightningApp();

// Performance monitoring
window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log(`⚡ Lightning fast load: ${Math.round(loadTime)}ms`);
    
    // Report Web Vitals
    if ('web-vital' in window) {
        // Will be implemented by web-vitals library if needed
    }
});

// Export for global access
window.LightningApp = app;
