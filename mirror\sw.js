// Service Worker for Game App
const CACHE_NAME = 'game-app-v1';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';

// Resources to cache immediately
const STATIC_ASSETS = [
    '/',
    '/index_modern.html',
    '/games.json',
    '/manifest.json',
    'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
    'https://fonts.googleapis.com/icon?family=Material+Symbols+Rounded'
];

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('SW: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('SW: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('SW: Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('SW: Failed to cache static assets', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('SW: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('SW: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('SW: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(request)) {
        event.respondWith(cacheFirst(request));
    } else if (isGameImage(request)) {
        event.respondWith(cacheWithNetworkFallback(request));
    } else if (isAPIRequest(request)) {
        event.respondWith(networkFirstWithCache(request));
    } else {
        event.respondWith(networkFirst(request));
    }
});

// Cache strategies

// Cache first - for static assets
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('SW: Cache first failed', error);
        return new Response('Offline', { status: 503 });
    }
}

// Cache with network fallback - for images
async function cacheWithNetworkFallback(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            // Only cache successful responses
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('SW: Image cache failed', error);
        // Return placeholder image or cached version
        const cachedResponse = await caches.match(request);
        return cachedResponse || createPlaceholderResponse();
    }
}

// Network first with cache fallback - for API requests
async function networkFirstWithCache(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('SW: Network first failed, trying cache', error);
        const cachedResponse = await caches.match(request);
        return cachedResponse || new Response('{"error": "Offline"}', {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Network first - for other requests
async function networkFirst(request) {
    try {
        return await fetch(request);
    } catch (error) {
        console.error('SW: Network request failed', error);
        const cachedResponse = await caches.match(request);
        return cachedResponse || new Response('Offline', { status: 503 });
    }
}

// Helper functions

function isStaticAsset(request) {
    const url = new URL(request.url);
    return STATIC_ASSETS.some(asset => url.pathname.includes(asset)) ||
           url.pathname.endsWith('.css') ||
           url.pathname.endsWith('.js') ||
           url.pathname.endsWith('.html') ||
           url.pathname.endsWith('.json');
}

function isGameImage(request) {
    const url = new URL(request.url);
    return url.pathname.includes('/assets/') ||
           url.hostname.includes('statics.hokibagus.club') ||
           url.pathname.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i);
}

function isAPIRequest(request) {
    const url = new URL(request.url);
    return url.pathname.includes('/api/') ||
           url.pathname.endsWith('.json');
}

function createPlaceholderResponse() {
    // Create a simple placeholder image response
    const svg = `
        <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f0f0f0"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">
                🎰 Game Image
            </text>
        </svg>
    `;
    
    return new Response(svg, {
        headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'no-cache'
        }
    });
}

// Background sync for offline actions
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        console.log('SW: Background sync triggered');
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // Sync any pending data when back online
        console.log('SW: Performing background sync');
        
        // Update games.json if needed
        const cache = await caches.open(DYNAMIC_CACHE);
        const response = await fetch('/games.json');
        if (response.ok) {
            await cache.put('/games.json', response);
            console.log('SW: Games data updated');
        }
    } catch (error) {
        console.error('SW: Background sync failed', error);
    }
}

// Push notifications (if needed in future)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/assets/favicon.png',
            badge: '/assets/favicon.png',
            vibrate: [100, 50, 100],
            data: data.data || {}
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow('/')
    );
});

console.log('SW: Service Worker loaded');
