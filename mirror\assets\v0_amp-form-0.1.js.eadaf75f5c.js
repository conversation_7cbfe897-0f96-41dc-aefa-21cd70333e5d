;
(self.AMP=self.AMP||[]).push({m:0,v:"2508201830000",n:"amp-form",ev:"0.1",l:!0,f:function(t,n){!function(){function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var i=0,r=new Array(n);i<n;i++)r[i]=t[i];return r}function i(t,i){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,i){if(t){if("string"==typeof t)return n(t,i);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,i):void 0}}(t))||i&&t&&"number"==typeof t.length){r&&(t=r);var e=0;return function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r;function e(){return r||(r=Promise.resolve(void 0))}var u=function(){var t=this;this.promise=new Promise((function(n,i){t.resolve=n,t.reject=i}))};function o(t){return new Promise((function(n){n(t())}))}var s=function(){function t(t){if(this.tnt=new u,this.nnt=0,t)for(var n,r=i(t,!0);!(n=r()).done;){var e=n.value;this.add(e)}}var n=t.prototype;return n.add=function(t){var n=this,i=++this.nnt;return t.then((function(t){n.nnt===i&&n.tnt.resolve(t)}),(function(t){n.nnt===i&&n.tnt.reject(t)})),this.tnt.promise},n.then=function(t,n){return this.tnt.promise.then(t,n)},t}();function a(t,n,i){return n in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i,t}function c(t){return t?Array.prototype.slice.call(t):[]}var f=Array.isArray;function h(t){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var l=Object.prototype,v=l.hasOwnProperty,d=l.toString;function m(t){return"[object Object]"===d.call(t)}function p(t){var n=Object.create(null);return t&&Object.assign(n,t),n}function b(t,n){return v.call(t,n)}function y(t,n,i,r,e,u,o,s,a,c,f){return t}var w="amp:dom-update",g="amp:form-value-change",x="name";function E(t){return(t.ownerDocument||t).defaultView}var T,O=/(\0)|^(-)$|([\x01-\x1f\x7f]|^-?[0-9])|([\x80-\uffff0-9a-zA-Z_-]+)|[^]/g;function j(t,n,i,r,e){return e||(n?"�":r?t.slice(0,-1)+"\\"+t.slice(-1).charCodeAt(0).toString(16)+" ":"\\"+t)}function A(t,n){return t.replace(/^|,/g,"$&".concat(n," "))}function R(t){return String(t).replace(O,j)}function P(t){y(/^[\w-]+$/.test(t))}function S(t,n){return P(n),n=n.toUpperCase(),function(t,i){for(var r=[],e=t.parentElement;e;e=e.parentElement)e.tagName==n&&r.push(e);return r}(t)}function I(t){var n;null===(n=t.parentElement)||void 0===n||n.removeChild(t)}function k(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function _(t,n){t.insertBefore(n,t.firstChild)}function N(t,n){for(var i=t.length,r=0;r<i;r++)n(t[r],r)}function U(t){return t.__AMP_FORM||null}function M(t){for(var n=t.elements,i={},r=/^(?:input|select|textarea)$/i,e=/^(?:submit|button|image|file|reset)$/i,u=/^(?:checkbox|radio)$/i,o=function(t){var o=n[t],s=o.checked,a=o.multiple,c=o.name,f=o.options,h=o.tagName,l=o.type,v=o.value;return!c||X(o)||!r.test(h)||e.test(l)||u.test(l)&&!s?"continue":(void 0===i[c]&&(i[c]=[]),a?(N(f,(function(t){t.selected&&i[c].push(t.value)})),"continue"):void i[c].push(v))},s=0;s<n.length;s++)o(s);var a=C(t);if(null!=a&&a.name){var c=a.name,f=a.value;void 0===i[c]&&(i[c]=[]),i[c].push(f)}return Object.keys(i).forEach((function(t){0==i[t].length&&delete i[t]})),i}function C(t){var n=t.elements,i=t.ownerDocument.activeElement,r=c(n).filter(F);return r.includes(i)?i:r[0]||null}function F(t){var n=t,i=n.tagName,r=n.type;return"BUTTON"==i||"submit"==r}function X(t){return t.disabled||S(t,"fieldset").some((function(t){return t.disabled}))}function D(t){var n=t,i=t;switch(t.type){case"select-multiple":case"select-one":return c(n.options).every((function(t){var n=t.defaultSelected;return t.selected===n}));case"checkbox":case"radio":return i.checked===i.defaultChecked;default:var r=i.defaultValue;return i.value===r}}var L=/(?:^[#?]?|&)([^=&]+)(?:=([^&]*))?/g;function $(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{return decodeURIComponent(t)}catch(t){return n}}function z(t){var n,i=p();if(!t)return i;for(;n=L.exec(t);){var r=$(n[1],n[1]),e=n[2]?$(n[2].replace(/\+/g," "),n[2]):"";i[r]=e}return i}self.__AMP_LOG=self.__AMP_LOG||{user:null,dev:null,userForEmbed:null};var G=self.__AMP_LOG;function V(t,n){throw new Error("failed to call initLogConstructor")}function q(t){return G.user||(G.user=B()),function(t,n){return n&&n.ownerDocument.defaultView!=t}(G.user.win,t)?G.userForEmbed||(G.userForEmbed=B()):G.user}function B(t){return V()}function H(){return G.dev||(G.dev=V())}function J(t,n,i,r,e,u,o,s,a,c,f){return t}function Z(t,n,i,r,e,u,o,s,a,c,f){return q().assert(t,n,i,r,e,u,o,s,a,c,f)}function K(t,n){return rt(t=function(t){return t.__AMP_TOP||(t.__AMP_TOP=t)}(t),n)}function W(t,n){return rt(it(nt(t)),n)}function Y(t,n){var i=it(nt(t));return ot(i,n)?rt(i,n):null}function Q(t,n){return function(t,n){var i=et(t,n);if(i)return i;var r,e,o,s,a=ut(t);return a[n]=(e=(r=new u).promise,o=r.reject,s=r.resolve,e.catch((function(){})),{obj:null,promise:e,resolve:s,reject:o,context:null,ctor:null}),a[n].promise}(it(t),n)}function tt(t,n){return et(it(t),n)}function nt(t){return t.nodeType?(n=E(t),K(n,"ampdoc")).getAmpDoc(t):t;var n}function it(t){var n=nt(t);return n.isSingleDoc()?n.win:n}function rt(t,n){J(ot(t,n));var i=ut(t)[n];return i.obj||(J(i.ctor),J(i.context),i.obj=new i.ctor(i.context),J(i.obj),i.context=null,i.resolve&&i.resolve(i.obj)),i.obj}function et(t,n){var i=ut(t)[n];return i?i.promise?i.promise:(rt(t,n),i.promise=Promise.resolve(i.obj)):null}function ut(t){var n=t.__AMP_SERVICES;return n||(n=t.__AMP_SERVICES={}),n}function ot(t,n){var i=t.__AMP_SERVICES&&t.__AMP_SERVICES[n];return!(!i||!i.ctor)}function st(t,n,i,r){var e=tt(t,n);if(e)return e;var u=nt(t);return u.whenExtensionsKnown().then((function(){var t=u.getExtensionVersion(i);return t?K(u.win,"extensions").waitForExtension(i,t):null})).then((function(i){return i?r?tt(t,n):Q(t,n):null}))}var at,ct=function(t){return nt(t)},ft=function(t){return st(t,"amp-analytics-instrumentation","amp-analytics")},ht=function(t){return W(t,"mutator")},lt=function(t){return Y(t,"url")},vt=function(t){return W(t,"viewport")};function dt(t,n,i,r){var e=t,u=i,o=function(t){try{return u(t)}catch(t){var n,i;throw null===(n=(i=self).__AMP_REPORT_ERROR)||void 0===n||n.call(i,t),t}},s=function(){if(void 0!==at)return at;at=!1;try{var t={get capture(){return at=!0,!1}};self.addEventListener("test-options",null,t),self.removeEventListener("test-options",null,t)}catch(t){}return at}(),a=!(null==r||!r.capture);return e.addEventListener(n,o,s?r:a),function(){null==e||e.removeEventListener(n,o,s?r:a),u=null,e=null,o=null}}function mt(t,n,i,r){var e={detail:i};if(Object.assign(e,r),"function"==typeof t.CustomEvent)return new t.CustomEvent(n,e);var u=t.document.createEvent("CustomEvent");return u.initCustomEvent(n,!!e.bubbles,!!e.cancelable,i),u}function pt(t,n,i,r){return dt(t,n,i,r)}function bt(t,n,i,r){var e,u=new Promise((function(r){e=function(t,n,i,r){var e=i,u=dt(t,n,(function(t){try{e(t)}finally{e=null,u()}}),r);return u}(t,n,r,i)}));return u.then(e,e),r&&r(e),u}function yt(t,n){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),i.push.apply(i,r)}return i}function wt(t,n){return(wt=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t})(t,n)}function gt(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&wt(t,n)}function xt(t){return(xt=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Et(t,n){if(n&&("object"===h(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Tt(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,r=xt(t);if(n){var e=xt(this).constructor;i=Reflect.construct(r,arguments,e)}else i=r.apply(this,arguments);return Et(this,i)}}function Ot(t,n){var i=K(t,"platform");return i.isIos()&&11==i.getMajorVersion()?new St(n):FormData.prototype.entries&&FormData.prototype.delete?new Pt(n):new Rt(n)}var jt,At,Rt=function(){function t(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;this.int=t?M(t):p()}var n=t.prototype;return n.append=function(t,n,i){var r=String(t);this.int[r]=this.int[r]||[],this.int[r].push(String(n))},n.delete=function(t){delete this.int[t]},n.entries=function(){var t=this,n=[];Object.keys(this.int).forEach((function(i){t.int[i].forEach((function(t){return n.push([i,t])}))}));var i=0;return{next:function(){return i<n.length?{value:n[i++],done:!1}:{value:void 0,done:!0}}}},n.getFormData=function(){var t=this,n=new FormData;return Object.keys(this.int).forEach((function(i){t.int[i].forEach((function(t){return n.append(i,t)}))})),n},t}(),Pt=function(){function t(t){this.rnt=new FormData(t),this.ent(t)}var n=t.prototype;return n.ent=function(t){if(t){var n=C(t);n&&n.name&&this.append(n.name,n.value)}},n.append=function(t,n,i){this.rnt.append(t,n)},n.delete=function(t){this.rnt.delete(t)},n.entries=function(){return this.rnt.entries()},n.getFormData=function(){return this.rnt},t}(),St=function(t){gt(i,t);var n=Tt(i);function i(t){var i;return i=n.call(this,t),t&&N(t.elements,(function(t){"file"==t.type&&0==t.files.length&&(i.rnt.delete(t.name),i.rnt.append(t.name,new Blob([]),""))})),i}return i.prototype.append=function(t,n,i){var r;n&&"object"==h(n)&&""==(r=n).name&&0==r.size?this.rnt.append(t,new Blob([]),i||""):this.rnt.append(t,n)},i}(Pt),It=function(){function t(t){this.G=t,this.K=0,this.Y=0,this.rr=p()}var n=t.prototype;return n.has=function(t){return!!this.rr[t]},n.get=function(t){var n=this.rr[t];if(n)return n.access=++this.Y,n.payload},n.put=function(t,n){this.has(t)||this.K++,this.rr[t]={payload:n,access:this.Y},this.nr()},n.nr=function(){if(!(this.K<=this.G)){var t,n=this.rr,i=this.Y+1;for(var r in n){var e=n[r].access;e<i&&(i=e,t=r)}void 0!==t&&(delete n[t],this.K--)}},t}(),kt=function(){return self.AMP.config.urls}(),_t=new Set(["c","v","a","ad"]),Nt="__amp_source_origin",Ut=function(t){return"string"==typeof t?Mt(t):t};function Mt(t,n){return jt||(jt=self.document.createElement("a"),At=self.__AMP_URL_CACHE||(self.__AMP_URL_CACHE=new It(100))),function(t,n,i){if(i&&i.has(n))return i.get(n);t.href=n,t.protocol||(t.href=t.href);var r,e={href:t.href,protocol:t.protocol,host:t.host,hostname:t.hostname,port:"0"==t.port?"":t.port,pathname:t.pathname,search:t.search,hash:t.hash,origin:null};"/"!==e.pathname[0]&&(e.pathname="/"+e.pathname),("http:"==e.protocol&&80==e.port||"https:"==e.protocol&&443==e.port)&&(e.port="",e.host=e.hostname),r=t.origin&&"null"!=t.origin?t.origin:"data:"!=e.protocol&&e.host?e.protocol+"//"+e.host:e.href,e.origin=r;var u=e;return i&&i.put(n,u),u}(jt,t,n?null:At)}function Ct(t,n,i){if(!n)return t;var r=t.split("#",2),e=r[0].split("?",2);return e[0]+(e[1]?i?"?".concat(n,"&").concat(e[1]):"?".concat(e[1],"&").concat(n):"?".concat(n))+(r[1]?"#".concat(r[1]):"")}function Ft(t,n){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(n))}function Xt(t){var n,i=[];for(var r in t){var e=t[r];if(null!=e){e=f(n=e)?n:[n];for(var u=0;u<e.length;u++)i.push(Ft(r,e[u]))}}return i.join("&")}function Dt(t){return kt.cdnProxyRegex.test(Ut(t).origin)}var Lt,$t=["GET","POST"];function zt(t,n){var i,r=function(t){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?yt(Object(i),!0).forEach((function(n){a(t,n,i[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):yt(Object(i)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(i,n))}))}return t}({},n);if((i=n.body)&&"function"==typeof i.getFormData){var e=n.body;r.headers["Content-Type"]="multipart/form-data;charset=utf-8",r.body=function(t){for(var n=[],i=t.next();!i.done;i=t.next())n.push(i.value);return n}(e.entries())}return{input:t,init:r}}var Gt=["Webkit","webkit","Moz","moz","ms","O","o"],Vt={"getPropertyPriority":function(){return""},"getPropertyValue":function(){return""}};function qt(t,n,i,r,e){var u=function(t,n,i){if(n.startsWith("--"))return n;Lt||(Lt=p());var r=Lt[n];if(!r||i){if(r=n,void 0===t[n]){var e=function(t){return t.charAt(0).toUpperCase()+t.slice(1)}(n),u=function(t,n){for(var i=0;i<Gt.length;i++){var r=Gt[i]+n;if(void 0!==t[r])return r}return""}(t,e);void 0!==t[u]&&(r=u)}i||(Lt[n]=r)}return r}(t.style,n,e);if(u){var o,s=r?i+r:i;t.style.setProperty((o=u.replace(/[A-Z]/g,(function(t){return"-"+t.toLowerCase()})),Gt.some((function(t){return o.startsWith(t+"-")}))?"-".concat(o):o),s)}}function Bt(t,n){void 0===n&&(n=t.hasAttribute("hidden")),n?t.removeAttribute("hidden"):t.setAttribute("hidden","")}function Ht(t){return"".concat(t,"px")}function Jt(t,n){return t.getComputedStyle(n)||Vt}var Zt="autoexpand",Kt=function(){function t(t){var n=t.getRootNode();this.ql=n.ownerDocument||n,this.t=J(this.ql.defaultView),this.eo=vt(t),this.Jy=[],this.Jy.push(pt(n,"input",(function(t){var n=t.target;"TEXTAREA"==n.tagName&&n.hasAttribute(Zt)&&Wt(n)}))),this.Jy.push(pt(n,"mousedown",(function(t){if(1==t.which){var n=t.target;"TEXTAREA"==n.tagName&&function(t){var n=ht(t);Promise.all([n.measureElement((function(){return t.scrollHeight})),bt(t,"mouseup")]).then((function(i){var r=i[0],e=0;return n.measureMutateElement(t,(function(){e=t.scrollHeight}),(function(){!function(t,n,i){n!=i&&t.removeAttribute(Zt)}(t,r,e)}))}))}(n)}})));var i=n.querySelectorAll("textarea");this.Jy.push(pt(n,w,(function(){i=n.querySelectorAll("textarea")})));var r,e=function(t,n,i){var r=0,e=null;function u(i){e=null,r=t.setTimeout(o,100),n.apply(null,i)}function o(){r=0,e&&u(e)}return function(){for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];r?e=n:u(n)}}(this.t,(function(t){t.relayoutAll&&i.forEach((function(t){"TEXTAREA"==t.tagName&&t.hasAttribute(Zt)&&Wt(t)}))}));this.Jy.push(this.eo.onResize(e)),r=i,Promise.all(c(r).map((function(t){return function(t){return ht(t).measureElement((function(){return t.scrollHeight>t.clientHeight}))}(t).then((function(n){n&&(q().warn("AMP-FORM",'"textarea[autoexpand]" with initially scrolling content will not autoexpand.\nSee https://github.com/ampproject/amphtml/issues/20839'),t.removeAttribute(Zt))}))})))}return t.install=function(n){var i=n.getRootNode(),r=null,e=function(){var e=i.querySelector("textarea[autoexpand]");if(!e||r)return!e&&r?(r.dispose(),void(r=null)):void 0;r=new t(n)};pt(i,w,e),e()},t.prototype.dispose=function(){this.Jy.forEach((function(t){return t()}))},t}();function Wt(t){var n=ht(t),i=J(t.ownerDocument.defaultView),r=0,e=0,u=0,o=function(t){var n=J(t.ownerDocument),i=J(n.defaultView),r=J(n.body),e=ht(t),u=t.cloneNode(!1);u.classList.add("i-amphtml-textarea-clone");var o=0,s=0,a=!1;return e.measureMutateElement(r,(function(){var n=Jt(i,t),r=parseInt(n.getPropertyValue("max-height"),10);o=parseInt(n.getPropertyValue("width"),10),a=isNaN(r)||t.scrollHeight<r}),(function(){a&&(t.scrollTop=0),qt(u,"width",Ht(o)),n.body.appendChild(u)})).then((function(){return e.measureMutateElement(r,(function(){s=u.scrollHeight}),(function(){I(u)}))})).then((function(){return s}))}(t);return n.measureMutateElement(t,(function(){var n=Jt(i,t);e=t.scrollHeight;var o=parseInt(n.getPropertyValue("max-height"),10);u=isNaN(o)?1/0:o,r="content-box"==n.getPropertyValue("box-sizing")?-parseInt(n.getPropertyValue("padding-top"),10)-parseInt(n.getPropertyValue("padding-bottom"),10):parseInt(n.getPropertyValue("border-top-width"),10)+parseInt(n.getPropertyValue("border-bottom-width"),10)}),(function(){return o.then((function(n){var o=n+r;t.classList.toggle("i-amphtml-textarea-max",o>u);var s="iAmphtmlHasExpanded"in t.dataset,a=/google/i.test(i.navigator.vendor)?3:0;(s||e<=n+a)&&(t.dataset.iAmphtmlHasExpanded="",qt(t,"height",Ht(n+r)))}))}))}var Yt={"INPUT":!0,"SELECT":!0,"TEXTAREA":!0},Qt=function(){function t(t,n){this.unt=t,this.t=n,this.ont=0,this.snt=p(),this.ant=null,this.cnt=!1,this.fnt=!1,this.Sj(),this.hnt()}var n=t.prototype;return n.onSubmitting=function(){this.cnt=!0,this.lnt()},n.onSubmitError=function(){this.cnt=!1,this.lnt()},n.onSubmitSuccess=function(){this.cnt=!1,this.ant=this.vnt(),this.dnt(),this.lnt()},n.vnt=function(){return Ot(this.t,this.unt).getFormData()},n.lnt=function(){var t=this.ont>0&&!this.cnt;if(t!==this.fnt){this.unt.classList.toggle("amp-form-dirty",t);var n=mt(this.t,"amp:form-dirtiness-change",{"isDirty":t},{bubbles:!0});this.unt.dispatchEvent(n)}this.fnt=t},n.Sj=function(){this.unt.addEventListener("input",this.mnt.bind(this)),this.unt.addEventListener("reset",this.pnt.bind(this)),this.unt.addEventListener(g,this.mnt.bind(this))},n.hnt=function(){for(var t=0;t<this.unt.elements.length;++t)this.bnt(this.unt.elements[t]);this.lnt()},n.mnt=function(t){var n=t.target;this.bnt(n),this.lnt()},n.pnt=function(t){this.dnt(),this.lnt()},n.bnt=function(t){(function(t){var n=t.hidden,i=t.name,r=t.tagName;return!Yt[r]||!i||n||X(t)})(t)||(function(t){switch(t.tagName){case"INPUT":return"checkbox"==t.type||"radio"==t.type?!t.checked:!t.value;case"TEXTAREA":return!t.value;case"SELECT":return!1;default:throw new Error("isFieldEmpty: ".concat(t.tagName," is not a supported field element."))}}(t)||D(t)||this.ynt(t)?this.wnt(t.name):this.gnt(t.name))},n.ynt=function(t){if(!this.ant)return!1;var n=t.name,i=t.value;return this.ant.get(n)===i},n.gnt=function(t){this.snt[t]||(this.snt[t]=!0,++this.ont)},n.wnt=function(t){this.snt[t]&&(this.snt[t]=!1,--this.ont)},n.dnt=function(){this.snt=p(),this.ont=0},t}(),tn="submit",nn=1,rn=2,en=1,un=2,on=3,sn={"acceptCharset":{access:nn,attr:"accept-charset"},"accessKey":{access:nn,attr:"accesskey"},"action":{access:nn,type:en},"attributes":{access:rn},"autocomplete":{access:nn,def:"on"},"children":{access:rn},"dataset":{access:rn},"dir":{access:nn},"draggable":{access:nn,type:un,def:!1},"elements":{access:rn},"encoding":{access:rn},"enctype":{access:nn},"hidden":{access:nn,type:on,def:!1},"id":{access:nn,def:""},"lang":{access:nn},"localName":{access:rn},"method":{access:nn,def:"get"},"name":{access:nn},"noValidate":{access:nn,attr:"novalidate",type:on,def:!1},"prefix":{access:rn},"spellcheck":{access:nn},"style":{access:rn},"target":{access:nn,def:""},"title":{access:nn},"translate":{access:nn}},an=function(){function t(){this.tt=null}var n=t.prototype;return n.add=function(t){var n=this;return this.tt||(this.tt=[]),this.tt.push(t),function(){n.remove(t)}},n.remove=function(t){var n,i,r;this.tt&&(i=t,-1!=(r=(n=this.tt).indexOf(i))&&n.splice(r,1))},n.removeAll=function(){this.tt&&(this.tt.length=0)},n.fire=function(t){if(this.tt)for(var n,r=i(this.tt.slice(),!0);!(n=r()).done;)(0,n.value)(t)},n.getHandlerCount=function(){var t,n;return null!==(t=null===(n=this.tt)||void 0===n?void 0:n.length)&&void 0!==t?t:0},t}(),cn=function(){function t(){this.ls=new an}var n=t.prototype;return n.beforeSubmit=function(t){return this.ls.add(t)},n.fire=function(t){this.ls.fire(t)},t}(),fn=function(){function t(t,n){this.ci=n,this.eo=vt(t),this.so=K(t.win,"vsync"),this.xnt=null,this.Ent="",this.Ln=!1,this.Tnt=t.win.document.createElement("div"),Bt(this.Tnt,!1),this.Tnt.classList.add("i-amphtml-validation-bubble"),this.Tnt.__BUBBLE_OBJ=this,t.getBody().appendChild(this.Tnt)}var n=t.prototype;return n.isActiveOn=function(t){return this.Ln&&t==this.xnt},n.hide=function(){this.Ln&&(this.Ln=!1,this.xnt=null,this.Ent="",this.so.run({measure:void 0,mutate:hn},{bubbleElement:this.Tnt}))},n.show=function(t,n){if(!this.isActiveOn(t)||n!=this.Ent){this.Ln=!0,this.xnt=t,this.Ent=n;var i={message:n,targetElement:t,bubbleElement:this.Tnt,viewport:this.eo,id:this.ci};this.so.run({measure:ln,mutate:vn},i)}},t}();function hn(t){t.bubbleElement.removeAttribute("aria-alert"),t.bubbleElement.removeAttribute("role"),k(t.bubbleElement),Bt(t.bubbleElement,!1)}function ln(t){t.targetRect=t.viewport.getLayoutRect(t.targetElement)}function vn(t){k(t.bubbleElement);var n=t.bubbleElement.ownerDocument.createElement("div");n.id="bubble-message-".concat(t.id),n.textContent=t.message,t.bubbleElement.setAttribute("aria-labeledby",n.id),t.bubbleElement.setAttribute("role","alert"),t.bubbleElement.setAttribute("aria-live","assertive"),t.bubbleElement.appendChild(n),Bt(t.bubbleElement,!0),function(t,n){for(var i in n)qt(t,i,n[i])}(t.bubbleElement,{top:"".concat(t.targetRect.top-10,"px"),left:"".concat(t.targetRect.left+t.targetRect.width/2,"px")})}var dn,mn,pn="__AMP_VALIDATION_",bn="Please match the requested format.",yn=0,wn=function(){function t(t){this.form=t,this.ampdoc=ct(t),this.mutator=ht(t),this.root=this.ampdoc.getRootNode(),this.Ont=null}var n=t.prototype;return n.report=function(){},n.onBlur=function(t){},n.onInput=function(t){},n.inputs=function(){return this.form.querySelectorAll("input,select,textarea")},n.checkInputValidity=function(t){if("TEXTAREA"===t.tagName&&t.hasAttribute("pattern")&&(t.checkValidity()||t.validationMessage===bn)){var n=t.getAttribute("pattern"),i=new RegExp("^".concat(n,"$"),"m").test(t.value);t.setCustomValidity(i?"":bn)}return t.checkValidity()},n.checkFormValidity=function(t){return this.jnt(t),t.checkValidity()},n.reportFormValidity=function(t){return this.jnt(t),t.reportValidity()},n.jnt=function(t){var n=this;N(t.elements,(function(t){"TEXTAREA"==t.tagName&&n.checkInputValidity(t)}))},n.fireValidityEventIfNecessary=function(){var t=this.Ont;if(this.Ont=this.checkFormValidity(this.form),t!==this.Ont){var n=mt(E(this.form),this.Ont?"valid":"invalid",null,{bubbles:!0});this.form.dispatchEvent(n)}},t}(),gn=function(t){gt(i,t);var n=Tt(i);function i(){return n.apply(this,arguments)}return i.prototype.report=function(){this.reportFormValidity(this.form),this.fireValidityEventIfNecessary()},i}(wn),xn=function(t){gt(i,t);var n=Tt(i);function i(t){var i;i=n.call(this,t);var r="i-amphtml-validation-bubble-".concat(yn++);return i.Ant=new fn(i.ampdoc,r),i}var r=i.prototype;return r.report=function(){for(var t=this.inputs(),n=0;n<t.length;n++)if(!this.checkInputValidity(t[n])){t[n].focus(),this.Ant.show(t[n],t[n].validationMessage);break}this.fireValidityEventIfNecessary()},r.onBlur=function(t){"submit"!=t.target.type&&this.Ant.hide()},r.onInput=function(t){var n=t.target;this.Ant.isActiveOn(n)&&(this.checkInputValidity(n)?(n.removeAttribute("aria-invalid"),this.Ant.hide()):(n.setAttribute("aria-invalid","true"),this.Ant.show(n,n.validationMessage)))},i}(wn),En=function(t){gt(i,t);var n=Tt(i);function i(t){var i;return(i=n.call(this,t)).Rnt=i.form.id?i.form.id:String(Date.now()+Math.floor(100*Math.random())),i.Pnt=0,i}var r=i.prototype;return r.reportInput=function(t){var n=function(t){var n=["badInput"];for(var i in t.validity)n.includes(i)||n.push(i);var r=n.filter((function(n){return!0===t.validity[n]}));return r.length?r[0]:null}(t);n&&this.showValidationFor(t,n)},r.Snt=function(){return"".concat("i-amphtml-aria-desc-").concat(this.Rnt,"-").concat(this.Pnt++)},r.hideAllValidations=function(){for(var t=this.inputs(),n=0;n<t.length;n++)this.hideValidationFor(t[n])},r.getValidationFor=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(!t.id)return null;var i=this.Int(t,n),r=pn+i;if(!(r in t)){var e="[visible-when-invalid=".concat(i,"]")+"[validation-for=".concat(t.id,"]");t[r]=this.root.querySelector(e)}return t[r]},r.Int=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=t.tagName,r=t.validationMessage;return"TEXTAREA"===i&&"customError"===n&&r===bn?"patternMismatch":n},r.showValidationFor=function(t,n){var i=this.getValidationFor(t,n);if(i){i.textContent.trim()||(i.textContent=t.validationMessage),t.__AMP_VISIBLE_VALIDATION=i;var r=i.getAttribute("id");r||(r=this.Snt(),i.setAttribute("id",r)),t.setAttribute("aria-invalid","true"),t.setAttribute("aria-describedby",r),this.mutator.mutateElement(i,(function(){return i.classList.add("visible")}))}},r.hideValidationFor=function(t){var n=this.getVisibleValidationFor(t);n&&(delete t.__AMP_VISIBLE_VALIDATION,t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),this.mutator.mutateElement(n,(function(){return n.classList.remove("visible")})))},r.getVisibleValidationFor=function(t){return t.__AMP_VISIBLE_VALIDATION},r.shouldValidateOnInteraction=function(t){throw Error("Not Implemented")},r.onInteraction=function(t){var n=t.target,i=!!n.checkValidity&&this.shouldValidateOnInteraction(n);this.hideValidationFor(n),i&&!this.checkInputValidity(n)&&this.reportInput(n)},r.onBlur=function(t){this.onInteraction(t)},r.onInput=function(t){this.onInteraction(t)},i}(wn),Tn=function(t){gt(i,t);var n=Tt(i);function i(){return n.apply(this,arguments)}var r=i.prototype;return r.report=function(){this.hideAllValidations();for(var t=this.inputs(),n=0;n<t.length;n++)if(!this.checkInputValidity(t[n])){this.reportInput(t[n]),t[n].focus();break}this.fireValidityEventIfNecessary()},r.shouldValidateOnInteraction=function(t){return!!this.getVisibleValidationFor(t)},i}(En),On=function(t){gt(i,t);var n=Tt(i);function i(){return n.apply(this,arguments)}var r=i.prototype;return r.report=function(){this.hideAllValidations();for(var t=null,n=this.inputs(),i=0;i<n.length;i++)this.checkInputValidity(n[i])||(t=t||n[i],this.reportInput(n[i]));t&&t.focus(),this.fireValidityEventIfNecessary()},r.shouldValidateOnInteraction=function(t){return!!this.getVisibleValidationFor(t)},i}(En),jn=function(t){gt(i,t);var n=Tt(i);function i(){return n.apply(this,arguments)}var r=i.prototype;return r.shouldValidateOnInteraction=function(t){return!0},r.onInteraction=function(n){t.prototype.onInteraction.call(this,n),this.fireValidityEventIfNecessary()},i}(En),An=function(t){gt(i,t);var n=Tt(i);function i(){return n.apply(this,arguments)}var r=i.prototype;return r.shouldValidateOnInteraction=function(t){return!0},r.onInteraction=function(n){t.prototype.onInteraction.call(this,n),this.fireValidityEventIfNecessary()},i}(On),Rn="__amp_form_verify",Pn=function(){function t(t){this.unt=t}var n=t.prototype;return n.onCommit=function(){return this.knt(),this._nt()?this.aJ():Promise.resolve({updatedElements:[],errors:[]})},n.aJ=function(){return Promise.resolve({updatedElements:[],errors:[]})},n._nt=function(){for(var t=this.unt.elements,n=0;n<t.length;n++){var i=t[n];if(!i.disabled&&!D(i))return!0}return!1},n.knt=function(){var t=this.unt.elements;t&&N(t,(function(t){t.setCustomValidity("")}))},t}(),Sn=function(t){gt(i,t);var n=Tt(i);function i(){return n.apply(this,arguments)}return i}(Pn),In=function(t){gt(i,t);var n=Tt(i);function i(t,i){var r;return(r=n.call(this,t)).Nnt=i,r.Unt=null,r.Mnt=[],r}var r=i.prototype;return r.aJ=function(){var t=this,n=this.Nnt().then((function(){return[]}),(function(t){return function(t){var n=t.response;return n?n.json().then((function(t){return t.verifyErrors||[]}),(function(){return[]})):Promise.resolve([])}(t)}));return this.Cnt(n).then((function(n){return t.Fnt(n)}))},r.Cnt=function(t){var n=this;if(!this.Unt){this.Unt=new s;var i=function(){return n.Unt=null};this.Unt.then(i,i)}return this.Unt.add(t)},r.Fnt=function(t){var n=this,i=[],r=this.Mnt;this.Mnt=t;for(var e=0;e<t.length;e++){var u=t[e],o=q().assertString(u.name,"Verification errors must have a name property"),s=q().assertString(u.message,"Verification errors must have a message property"),a=q().assertElement(this.unt.querySelector('[name="'.concat(o,'"]')),"Verification error name property must match a field name");a.checkValidity()&&(a.setCustomValidity(s),i.push(a))}var c=r.filter((function(n){return t.every((function(t){return n.name!==t.name}))})).map((function(t){return n.unt.querySelector('[name="'.concat(t.name,'"]'))}));return{updatedElements:i.concat(c),errors:t}},i}(Pn),kn=function(){function t(t,n,i){this.Le=n,this.uw=i,this.gJ=t}var n=t.prototype;return n.isEnabled=function(){var t=this.Le.getAmpDoc();return!(!t.isSingleDoc()||!t.getRootNode().documentElement.hasAttribute("allow-viewer-render-template"))&&this.Le.hasCapability("viewerRenderTemplate")},n.assertTrustedViewer=function(t){return this.Le.isTrustedViewer().then((function(n){Z(n,"Refused to attempt SSR in untrusted viewer: ",t)}))},n.ssr=function(t,n){var i,r=this,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return e||(i=this.uw.maybeFindTemplate(t)),this.assertTrustedViewer(t).then((function(){return r.Le.sendMessageAwaitResponse("viewerRenderTemplate",r.jJ(n,i,e,u))}))},n.applySsrOrCsrTemplate=function(t,n){var i,r=this;return this.isEnabled()?(Z("string"==typeof n.html,"Skipping template rendering due to failed fetch"),i=this.assertTrustedViewer(t).then((function(){return r.uw.findAndSetHtmlForTemplate(t,n.html)}))):i=f(n)?this.uw.findAndRenderTemplateArray(t,n):this.uw.findAndRenderTemplate(t,n),i},n.jJ=function(t,n,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},e={"type":this.gJ},u="successTemplate",o=i&&i[u]?i[u]:n;o&&(e[u]={"type":"amp-mustache","payload":o.innerHTML});var s="errorTemplate",a=i&&i[s]?i[s]:null;a&&(e[s]={"type":"amp-mustache","payload":a.innerHTML}),r&&Object.assign(e,r);var c={"originalRequest":zt(t.xhrUrl,t.fetchOpt),"ampComponent":e};return c},t}();function _n(t,n,i){if(n[i])return n[i];var r=t.querySelector("style[".concat(i,"], link[").concat(i,"]"));return r?(n[i]=r,r):null}function Nn(t,n){for(var i=t.styleSheets,r=0;r<i.length;r++)if(i[r].ownerNode==n)return!0;return!1}var Un="amp-form",Mn=["amp-selector"],Cn="initial",Fn="verifying",Xn="submitting",Dn="valid",Ln="invalid",$n="AMP-Redirect-To",zn=function(){function t(t,n){var i,r,e=this;try{!function(t){var n,i=((n=E(t)).FormProxy||(n.FormProxy=function(t){function n(t){this.unt=t}var i=n.prototype,r=t.Object,e=r.prototype;return[t.HTMLFormElement,t.EventTarget].reduce((function(t,n){for(var i=n&&n.prototype;i&&i!==e&&!(t.indexOf(i)>=0);)t.push(i),i=r.getPrototypeOf(i);return t}),[]).forEach((function(n){var r=function(r){var u=t.Object.getOwnPropertyDescriptor(n,r);if(!u||r.toUpperCase()==r||r.startsWith("on")||e.hasOwnProperty.call(i,r))return"continue";if("function"==typeof u.value){var o=u.value;i[r]=function(){return o.apply(this.unt,arguments)}}else{var s={};u.get&&(s.get=function(){return u.get.call(this.unt)}),u.set&&(s.set=function(t){return u.set.call(this.unt,t)}),t.Object.defineProperty(i,r,s)}};for(var u in n)r(u)})),n}(n)),n.FormProxy),r=new i(t);"action"in r||function(t,n){var i=t.ownerDocument.defaultView.HTMLFormElement.prototype.cloneNode.call(t,!1),r=function(i){if(i in n||i.toUpperCase()==i||i.startsWith("on"))return"continue";var r=sn[i],e=t[i];if(r)if(r.access==rn){var u;if(e&&e.nodeType){var o=e,s=o.nextSibling,a=o.parentNode;a.removeChild(o);try{u=t[i]}finally{a.insertBefore(o,s)}}else u=e;Object.defineProperty(n,i,{get:function(){return u}})}else if(r.access==nn){var c=r.attr||i;Object.defineProperty(n,i,{get:function(){var i=n.getAttribute(c);if(null==i&&void 0!==r.def)return r.def;if(r.type==un)return"true"===i;if(r.type==on)return null!=i;if(r.type==en){var e=i||"";return lt(t).parse(e).href}return i},set:function(t){r.type==on&&(t=t?"":null),null!=t?n.setAttribute(c,t):n.removeAttribute(c)}})}else J(!1);else Object.defineProperty(n,i,{get:function(){return t[i]},set:function(n){t[i]=n}})};for(var e in i)r(e)}(t,r),t.$p=r}(t)}catch(t){H().error(Un,"form proxy failed to install",t)}!function(t,n){t.__AMP_FORM=n}(t,this),this.ci=n,this.ql=t.ownerDocument,this.t=this.ql.defaultView,this.ce=rt(this.t,"timer"),this.unt=t,this.Ni=ct(this.unt),this.Xnt=null,this.Dnt=function(t){return Y(t,"url-replace")}(this.Ni),this.uw=W(this.Ni,"templates"),this._t=K(this.t,"xhr"),this.Qm=function(t){return Y(t,"action")}(this.Ni),this.Zu=ht(this.Ni),this.Le=W(this.Ni,"viewer"),this.m6=new kn(Un,this.Le,this.uw),this.qq=(this.unt.getAttribute("method")||"GET").toUpperCase(),this.h=this.unt.getAttribute("target"),this.Lnt=this.$nt("action-xhr"),this.znt=this.$nt("verify-xhr"),this.Gnt=this.Vnt("enctype"),this.qnt=!this.unt.hasAttribute("novalidate"),this.unt.setAttribute("novalidate",""),this.qnt||this.unt.setAttribute("amp-novalidate",""),this.unt.classList.add("i-amphtml-form"),this.mi=Cn;for(var u,o,s=this.unt.elements,a=0;a<s.length;a++){var c=s[a].name;Z(c!=Nt&&c!=Rn,"Illegal input name, %s found: %s",c,s[a])}this.Bnt=new Qt(this.unt,this.t),this.ZU=function(t){switch(t.getAttribute("custom-validation-reporting")){case"as-you-go":return new jn(t);case"show-all-on-submit":return new On(t);case"interact-and-submit":return new An(t);case"show-first-on-submit":return new Tn(t)}return t.ownerDocument&&void 0===dn&&(dn=!!document.createElement("form").reportValidity),dn?new gn(t):new xn(t)}(this.unt),this.Hnt=(o=function(){return e.Jnt()},(u=this.unt).hasAttribute("verify-xhr")?new In(u,o):new Sn(u)),this.Qm.addToAllowlist("FORM",["clear","submit"],["email"]),this.Qm.installActionHandler(this.unt,this.vnn.bind(this)),this.Sj(),this.Znt(),this.Knt(),this.Wnt=null,this.Ynt=null,this.Qnt=null,function(t){return Q(t,"form-submit-service")}(t).then((function(t){e.Qnt=t})),this.cit=this.ql&&(i=this.ql,r=i.documentElement,["⚡4email","amp4email"].some((function(t){return r.hasAttribute(t)})))}var n=t.prototype;return n.$nt=function(t){var n=this.unt.getAttribute(t);if(n){var i=lt(this.Ni);i.assertHttpsUrl(n,this.unt,t),Z(!i.isProxyOrigin(n),"form %s should not be on AMP CDN: %s",t,this.unt)}return n},n.Vnt=function(t){var n=this.unt.getAttribute(t);return"application/x-www-form-urlencoded"===n||"multipart/form-data"===n?n:(null!==n&&q().warn(Un,"Unexpected enctype: ".concat(n,". Defaulting to 'multipart/form-data'.")),"multipart/form-data")},n.getXssiPrefix=function(){return this.unt.getAttribute("xssi-prefix")},n.requestForFormFetch=function(t,n,i,r){var e,u,o={"Accept":"application/json"};if("GET"==n||"HEAD"==n){this.fit();var s=this.lit();r&&r.forEach((function(t){return delete s[t]})),i&&function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=[],e=[];for(e.push({t:t,s:n,d:0});e.length>0;){var u=e.shift(),o=u.d,s=u.s,a=u.t;if(r.includes(s))throw new Error("Source object has a circular reference.");if(r.push(s),a!==s)if(o>i)Object.assign(a,s);else for(var c=0,f=Object.keys(s);c<f.length;c++){var h=f[c],l=s[h];if(b(a,h)){var v=a[h];if(m(l)&&m(v)){e.push({t:v,s:l,d:o+1});continue}}a[h]=l}}}(s,i),e=function(t,n){return Ct(t,Xt(n))}(t,s)}else for(var a in e=t,"application/x-www-form-urlencoded"===this.Gnt?(u=Xt(this.lit()),o={"Accept":"application/json","Content-Type":"application/x-www-form-urlencoded"}):(J("multipart/form-data"===this.Gnt),u=Ot(this.t,this.unt)),r&&r.forEach((function(t){return u.delete(t)})),i)u.append(a,i[a]);return{xhrUrl:e,fetchOpt:{"body":u,"method":n,"credentials":"include","headers":o}}},n.setXhrAction=function(t){this.Lnt=t},n.vnn=function(t){var n=this;return t.satisfiesTrust(2)?"submit"==t.method?this.vit().then((function(){return n.dit(t)})):("clear"===t.method&&this.mit(),null):null},n.vit=function(){if(this.Xnt)return this.Xnt;var t=c(this.unt.querySelectorAll(Mn.join(","))).map((function(t){return t.build()}));return this.Xnt=this.pit(t,2e3)},n.Sj=function(){var t=this;this.Ni.whenNextVisible().then((function(){var n=t.unt.querySelector("[autofocus]");n&&function(t){try{t.focus()}catch(t){}}(n)})),this.unt.addEventListener("submit",this.bit.bind(this),!0),this.unt.addEventListener("blur",(function(n){Bn(n.target),t.ZU.onBlur(n)}),!0),this.unt.addEventListener(g,(function(n){Bn(n.target),t.ZU.onInput(n)}),!0),this.m6.isEnabled()||this.unt.addEventListener("change",(function(n){t.Hnt.onCommit().then((function(i){var r=i.errors;i.updatedElements.forEach(Bn),t.ZU.onBlur(n),t.mi===Fn&&(r.length?(t.TA("verify-error"),t.yit({"verifyErrors":r}).then((function(){t.wit("verify-error",r,2)}))):t.TA(Cn))}))})),this.unt.addEventListener("input",(function(n){Bn(n.target),t.ZU.onInput(n)}))},n.Znt=function(){var t;(t=this.Ni,st(t,"inputmask","amp-inputmask")).then((function(t){t&&t.install()}))},n.git=function(t){this.xit(!1,"Form analytics not supported");var n={},i=this.lit();for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(n["formFields["+r+"]"]=i[r].join(","));n.formId=this.unt.id;try{this.Eit(t,n)}catch(t){H().error(Un,"Sending analytics failed:",t)}},n.dit=function(t){return this.mi!=Xn&&this.Tit()?this.Oit(t.trust,null):Promise.resolve(null)},n.mit=function(){this.unt.reset(),this.TA(Cn),this.unt.classList.remove("user-valid"),this.unt.classList.remove("user-invalid"),this.unt.querySelectorAll(".user-valid, .user-invalid").forEach((function(t){t.classList.remove("user-valid"),t.classList.remove("user-invalid")})),this.unt.querySelectorAll(".visible[validation-for]").forEach((function(t){t.classList.remove("visible")})),function(t){var n=document.createElement("input"),i=function(n){t.querySelectorAll(".".concat(R(n))).forEach((function(t){t.classList.remove(n)}))};for(var r in n.validity)i(r)}(this.unt)},n.bit=function(t){return this.mi!=Xn&&this.Tit()?((this.Lnt||"POST"==this.qq)&&t.preventDefault(),this.Oit(3,t)):(t.stopImmediatePropagation(),t.preventDefault(),Promise.resolve(null))},n.Oit=function(t,n){var i=this;try{var r={form:this.unt,actionXhrMutator:this.setXhrAction.bind(this)};J(this.Qnt).fire(r)}catch(t){H().error(Un,"Form submit service failed: %s",t)}var u=this.jit(),o=this.unt.getElementsByClassName("i-amphtml-async-input");if(this.Bnt.onSubmitting(),!this.Lnt&&"GET"==this.qq){if(this.xit(!1,"Non-XHR GETs not supported."),this.fit(),0===o.length){for(var s=0;s<u.length;s++)this.Dnt.expandInputValueSync(u[s]);var a=!n;return this.Ait(a),this.Bnt.onSubmitSuccess(),e()}n&&n.preventDefault()}this.TA(Xn);var c=[],f=[];return f.push(this.Rit(u)),N(o,(function(t){var n=i.Pit(t);t.classList.contains("i-async-require-action")?c.push(n):f.push(n)})),Promise.all(c).then((function(){return i.pit(f,1e4).then((function(){return i.Sit(t)}),(function(n){return i.Iit(n,t)}))}),(function(n){return i.Iit(n,t)}))},n.Iit=function(t,n){var i={};return t&&t.message&&(i.error=t.message),this.kit(t,i,n)},n.jit=function(){return this.unt.querySelectorAll('[type="hidden"][data-amp-replace]')},n.Sit=function(t){return this.Lnt?this._it(t):("POST"==this.qq?this.Nit():"GET"==this.qq&&this.Ait(!0),e())},n.Jnt=function(){var t=this;return this.mi===Xn?e():(this.TA(Fn),this.wit("verify",null,3),this.Rit(this.jit()).then((function(){return t.Uit()})))},n._it=function(t){var n,i=this;return this.m6.isEnabled()?n=this.Mit(t):(this.Cit(t),n=this.Fit().then((function(n){return i.Xit(n,t)}),(function(n){return i.Dit(n,t)}))),n},n.Mit=function(t){var n=this,i=this.lit();return this.yit(i).then((function(){return n.Qm.trigger(n.unt,tn,null,t)})).then((function(){var t,i,r,e=n.requestForFormFetch(n.Lnt,n.qq);return e.fetchOpt=function(t,n){var i,r=t||{},e=r.credentials;return y(void 0===e||"include"==e||"omit"==e),r.method=void 0===(i=r.method)?"GET":(i=i.toUpperCase(),y($t.includes(i)),i),r.headers=r.headers||{},y(null!==r.body),r}(e.fetchOpt),e.fetchOpt=function(t,n,i){i=i||{};var r=function(t){return t.origin||Mt(t.location.href).origin}(t);return r==Mt(n).origin&&(i.headers=i.headers||{},i.headers["AMP-Same-Origin"]="true"),i}(n.t,e.xhrUrl,e.fetchOpt),e.xhrUrl=(t=n.t,i=e.xhrUrl,r=e.fetchOpt,y("string"==typeof i),!1!==r.ampCors&&(i=function(t,n){return function(t){var n=z(Mt(t).search);Z(!(Nt in n),"Source origin is not allowed in %s",t)}(n),function(t,n,i,r){return Ct(t,Ft("__amp_source_origin",i),void 0)}(n,0,function(t){return Mt(function(t){if(!Dt(t=Ut(t)))return t.href;var n=t.pathname.split("/"),i=n[1];Z(_t.has(i),"Unknown path prefix in url %s",t.href);var r=n[2],e="s"==r?"https://"+decodeURIComponent(n[3]):"http://"+decodeURIComponent(r);return Z(e.indexOf(".")>0,"Expected a . in origin %s",e),n.splice(1,"s"==r?3:2),e+n.join("/")+function(t,n){if(!t||"?"==t)return"";var i=new RegExp("[?&]".concat("(amp_(js[^&=]*|gsa|r|kit)|usqp)","\\b[^&]*"),"g"),r=t.replace(i,"").replace(/^[?&]/,"");return r?"?"+r:""}(t.search)+(t.hash||"")}(t)).origin}(t.location.href))}(t,i)),i),n.m6.ssr(n.unt,e,n.Lit())})).then((function(i){return n.$it(i,t)}),(function(i){var r={};return i&&i.message&&(r.error=i.message),n.kit(i,r,t)}))},n.Lit=function(){var t,n,i=this.unt.querySelector("[submit-success]");i&&(t=this.uw.maybeFindTemplate(i));var r=this.unt.querySelector("[submit-error]");return r&&(n=this.uw.maybeFindTemplate(r)),{successTemplate:t,errorTemplate:n}},n.$it=function(t,n){var i=t.init,r=function(t,n){try{return function(t){return JSON.parse(t)}(t)}catch(t){return null==n||n(t),null}}(t.body,(function(t){return q().error(Un,"Failed to parse response JSON: %s",t)}));if(i){var e=i.status;if(e>=300)return this.kit(e,t,n,r)}return this.zit(t,n,r)},n.Cit=function(t){var n=this;this.git("amp-form-submit");var i=this.lit();this.yit(i).then((function(){n.Qm.trigger(n.unt,tn,null,t)}))},n.Rit=function(t){for(var n=[],i=0;i<t.length;i++)n.push(this.Dnt.expandInputValueAsync(t[i]));return this.pit(n,100)},n.Pit=function(t){var n=this;return t.getImpl().then((function(t){return t.getValue()})).then((function(i){var r,e,u=t.getAttribute(x),o=n.unt.querySelector("input[name=".concat(R(u),"]"));o||(r=n.t.document,e={"name":t.getAttribute(x),"hidden":"true"},o=function(t,n){for(var i in n)t.setAttribute(i,n[i]);return t}(r.createElement("input"),e)),o.setAttribute("value",i),n.unt.appendChild(o)}))},n.Fit=function(){return this.Nnt(this.Lnt,this.qq)},n.Uit=function(){var t=c(this.unt.querySelectorAll("[".concat(R("no-verify"),"]"))).map((function(t){return t.name||t.id}));return this.Nnt(this.znt,this.qq,a({},Rn,!0),t)},n.Nnt=function(t,n,i,r){this.xit(!1,"XHRs should be proxied.");var e=this.requestForFormFetch(t,n,i,r);return this._t.fetch(e.xhrUrl,e.fetchOpt)},n.Git=function(t){return t-1},n.Xit=function(t,n){var i=this;return this._t.xssiJson(t,this.getXssiPrefix()).then((function(t){return i.zit(t,n)}),(function(t){return q().error(Un,"Failed to parse response JSON: %s",t)})).then((function(){i.git("amp-form-submit-success"),i.Vit(t)}))},n.zit=function(t,n,i){var r=this;return this.TA("submit-success"),o((function(){r.yit(t||{}).then((function(){var e=r.Git(n);r.wit("submit-success",void 0===i?t:i,e),r.Bnt.onSubmitSuccess()}))}))},n.Dit=function(t,n){var i,r=this;if(t&&t.response){var e=t;i=this._t.xssiJson(e.response,this.getXssiPrefix()).catch((function(){return null}))}else i=Promise.resolve(null);return i.then((function(i){r.kit(t,i,n),r.git("amp-form-submit-error"),r.Vit(t.response)}))},n.kit=function(t,n,i,r){var e=this;return this.TA("submit-error"),q().error(Un,"Form submission failed: %s",t),o((function(){e.yit(n).then((function(){var t=e.Git(i);e.wit("submit-error",void 0===r?n:r,t),e.Bnt.onSubmitError()}))}))},n.Nit=function(){Z(!1,"Only XHR based (via action-xhr attribute) submissions are supported for POST requests. %s",this.unt)},n.Ait=function(t){this.git("amp-form-submit"),t&&this.unt.submit(),this.TA(Cn)},n.xit=function(t,n){Z(this.m6.isEnabled()===t,"[amp-form]: viewerRenderTemplate | %s",n)},n.fit=function(){Z(0==this.unt.querySelectorAll("input[type=password],input[type=file]").length,"input[type=password] or input[type=file] may only appear in form[method=post]")},n.Tit=function(){if(i=this.t.document,void 0===mn&&(mn=!!i.createElement("input").checkValidity),mn){var t=((n=this.unt).querySelectorAll("input,select,textarea,fieldset").forEach((function(t){return qn(t)})),qn(n));if(this.qnt)return this.ZU.report(),t}var n,i;return!0},n.Vit=function(t){if(this.xit(!1,"Redirects not supported."),t&&t.headers){var n=t.headers.get($n);if(n){Z(!this.cit,"Redirects not supported in AMP4Email.",this.unt),Z("_blank"!=this.h,"Redirecting to target=_blank using AMP-Redirect-To is currently not supported, use target=_top instead. %s",this.unt);try{var i=lt(this.Ni);i.assertAbsoluteHttpOrHttpsUrl(n),i.assertHttpsUrl(n,"AMP-Redirect-To","Url")}catch(t){Z(!1,"The `AMP-Redirect-To` header value must be an absolute URL starting with https://. Found %s",n)}(r=this.Ni,W(r,"navigation")).navigateTo(this.t,n,$n)}}var r},n.wit=function(t,n,i){var r=mt(this.t,"".concat(Un,".").concat(t),{"response":n});this.Qm.trigger(this.unt,t,r,i)},n.pit=function(t,n){return Promise.race([Promise.all(t),this.ce.promise(n)])},n.Eit=function(t,n){!function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];ft(t).then((function(e){e&&e.triggerEventForTarget(t,n,i,r)}))}(this.unt,t,n)},n.lit=function(){return M(this.unt)},n.TA=function(t){var n=this.mi;this.unt.classList.remove("amp-form-".concat(n)),this.unt.classList.add("amp-form-".concat(t)),this.qit(n),this.mi=t},n.yit=function(t){var n=this;f(t)&&(t={},q().warn(Un,"Unexpected data type: ".concat(t,". Expected non JSON array.")));var i=this.unt.querySelector("[".concat(this.mi,"]")),r=e();if(i){var u="rendered-message-".concat(this.ci);i.setAttribute("role","alert"),i.setAttribute("aria-labeledby",u),i.setAttribute("aria-live","assertive"),this.uw.hasTemplate(i)?r=this.m6.applySsrOrCsrTemplate(J(i),t).then((function(t){var r;return f(t)?1===t.length?r=t[0]:(r=document.createElement("div"),t.forEach((function(t){return r.appendChild(t)}))):r=t,r.id=u,r.setAttribute("i-amphtml-rendered",""),n.Zu.mutateElement(i,(function(){i.appendChild(r);var t=mt(n.t,w,null,{bubbles:!0});i.dispatchEvent(t)}))})):this.Zu.mutateElement(i,(function(){}))}return r},n.qit=function(t){var n=this.unt.querySelector("[".concat(t,"]"));if(n){var i,r=(i=n,P("i-amphtml-rendered"),function(t,n){if(void 0!==T?T:T=function(t){try{var n=t.ownerDocument,i=n.createElement("div"),r=n.createElement("div");return i.appendChild(r),i.querySelector(":scope div")===r}catch(t){return!1}}(t))return t.querySelector(A(n,":scope"));var i=function(t,n){var i=t.classList,r="i-amphtml-scoped";i.add(r);var e=A(n,".".concat(r)),u=t.querySelectorAll(e);return i.remove(r),u}(t,n)[0];return void 0===i?null:i}(i,"> [".concat("i-amphtml-rendered","]")));r&&I(r)}},n.Knt=function(){var t=this;if(!Dt(this.t.location)&&this.unt.hasAttribute("data-initialize-from-url")){var n=["SELECT","TEXTAREA"],i=["color","date","datetime-local","email","hidden","month","number","range","search","tel","text","time","url","week"],r=["checkbox","radio"],e=function(t,e){if(!t.hasAttribute("data-amp-replace")&&t.hasAttribute("data-allow-initialization")){var o=u[e]||"",s=t.getAttribute("type")||"text",a=t.tagName;if("INPUT"===a){if(i.includes(s.toLocaleLowerCase()))t.value!==o&&(t.value=o);else if(r.includes(s)){var c=t.value===o;t.checked!==c&&(t.checked=c)}}else n.includes(a)&&t.value!==o&&(t.value=o)}},u=z(this.t.location.search);Object.keys(u).forEach((function(n){var i=t.unt.elements[n];i&&(i.nodeType===Node.ELEMENT_NODE?e(i,n):i.length&&i.forEach((function(t){return e(t,n)})))}))}},n.renderTemplatePromiseForTesting=function(){return this.Ynt},n.xhrSubmitPromiseForTesting=function(){return this.Wnt},t}();function Gn(t){return t.classList.contains("user-valid")?Dn:t.classList.contains("user-invalid")?Ln:"none"}function Vn(t){if(t.validity)for(var n in t.validity)t.classList.toggle(n,t.validity[n])}function qn(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t.checkValidity)return!0;var i=!1,r=Gn(t),e=t.checkValidity();if(r!=Dn&&e?(t.classList.add("user-valid"),t.classList.remove("user-invalid"),i=r==Ln):r==Ln||e||(t.classList.add("user-invalid"),t.classList.remove("user-valid"),i=!0),Vn(t),n&&i){for(var u=S(t,"fieldset"),o=0;o<u.length;o++)qn(u[o]);t.form&&qn(t.form)}return e}function Bn(t){qn(t,!0)}var Hn=function(){function t(t){var n=this;this.Bit=this.Hit(t).then((function(){return n.Jit(t)}))}var n=t.prototype;return n.whenInitialized=function(){return this.Bit},n.Hit=function(t){var n=new u;return function(t,n,i,r,e){var u=t.getHeadNode(),o=function(t,n,i,r){var e=t.__AMP_CSS_SM;e||(e=t.__AMP_CSS_SM=p());var u="amp-extension=".concat(r);if(u){var o=_n(t,e,u);if(o)return"STYLE"==o.tagName&&o.textContent!==n&&(o.textContent=n),o}var s=(t.ownerDocument||t).createElement("style");s.textContent=n;return s.setAttribute("amp-extension",r),function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(i){var r=i.nextSibling;t.insertBefore(n,r)}else _(t,n)}(t,s,_n(t,e,"amp-runtime")),u&&(e[u]=s),s}(u,function(t,n){var i=t.__AMP_CSS_TR;return i?i(n):n}(u,'form.amp-form-submit-error [submit-error],form.amp-form-submit-success [submit-success],form.amp-form-submitting [submitting]{display:block}textarea[autoexpand]:not(.i-amphtml-textarea-max){overflow:hidden!important}.i-amphtml-textarea-clone{visibility:hidden;position:absolute;top:-9999px;left:-9999px;height:0!important}.i-amphtml-validation-bubble{transform:translate(-50%,-100%);background-color:#fff;box-shadow:0 5px 15px 0 rgba(0,0,0,.5);max-width:200px;position:absolute;display:block;box-sizing:border-box;padding:10px;border-radius:5px}.i-amphtml-validation-bubble:after{content:" ";position:absolute;bottom:-8px;left:30px;width:0;height:0;border-left:8px solid transparent;border-right:8px solid transparent;border-top:8px solid #fff}[visible-when-invalid]{color:red}\n/*# sourceURL=/extensions/amp-form/0.1/amp-form.css*/'),0,"amp-form");if(i){var s=t.getRootNode();if(Nn(s,o))return i(o),o;var a=setInterval((function(){Nn(s,o)&&(clearInterval(a),i(o))}),4)}}(t,0,n.resolve),n.promise},n.Jit=function(t){var n=this;return t.whenReady().then((function(){var i=t.getRootNode();n.Zit(i.querySelectorAll("form")),Kt.install(t),n.Kit(i),n.Wit(i)}))},n.Zit=function(t){t&&t.forEach((function(t,n){U(t)||new zn(t,"amp-form-".concat(n))}))},n.Kit=function(t){var n=this;t.addEventListener(w,(function(){n.Zit(t.querySelectorAll("form"))}))},n.Wit=function(t){t.addEventListener("keydown",(function(t){if(!t.defaultPrevented&&"Enter"==t.key&&(t.ctrlKey||t.metaKey)&&"TEXTAREA"===t.target.tagName){var n=t.target.form,i=n?U(n):null;i&&(i.bit(t),t.preventDefault())}}))},t}();t.registerServiceForDoc("form-submit-service",cn),t.registerServiceForDoc(Un,Hn)}();
/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */}});
//# sourceMappingURL=amp-form-0.1.js.map