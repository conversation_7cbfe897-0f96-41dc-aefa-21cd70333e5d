;
(self.AMP=self.AMP||[]).push({m:0,v:"2508201830000",n:"amp-list",ev:"0.1",l:!0,f:function(t,i){!function(){function i(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function n(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,n){if(t){if("string"==typeof t)return i(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){r&&(t=r);var e=0;return function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r;function e(){return r||(r=Promise.resolve(void 0))}var o=function(){var t=this;this.promise=new Promise((function(i,n){t.resolve=i,t.reject=n}))};function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,i){return(s=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,i){if(i&&("object"===a(i)||"function"==typeof i))return i;if(void 0!==i)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function f(t){return t?Array.prototype.slice.call(t):[]}var h=Array.isArray,c=Object.prototype,m=c.hasOwnProperty,d=c.toString;function v(t){var i=Object.create(null);return t&&Object.assign(i,t),i}function p(t,i){return m.call(t,i)}function b(t,i){if("."==i)return t;for(var r,e=t,o=n(i.split("."),!0);!(r=o()).done;){var s=r.value;if(!(s&&e&&void 0!==e[s]&&"object"==a(e)&&p(e,s))){e=void 0;break}e=e[s]}return e}function g(t,i,n,r,e,o,a,s,u,l,f){return t}var y,w="amp:dom-update";function x(t){return void 0!==y?y:y=function(t){try{var i=t.ownerDocument,n=i.createElement("div"),r=i.createElement("div");return n.appendChild(r),n.querySelector(":scope div")===r}catch(t){return!1}}(t)}function O(t,i){return t.replace(/^|,/g,"$&".concat(i," "))}function j(t,i){var n=t.classList,r="i-amphtml-scoped";n.add(r);var e=O(i,".".concat(r)),o=t.querySelectorAll(e);return n.remove(r),o}function S(t,i){if(x(t))return t.querySelector(O(i,":scope"));var n=j(t,i)[0];return void 0===n?null:n}function R(t,i){return g(/^[\w-]+$/.test(i)),S(t,"> [".concat(i,"]"))}function E(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function k(t){try{t.focus()}catch(t){}}var I,T={NODISPLAY:"nodisplay",FIXED:"fixed",FIXED_HEIGHT:"fixed-height",RESPONSIVE:"responsive",CONTAINER:"container",FILL:"fill",FLEX_ITEM:"flex-item",FLUID:"fluid",INTRINSIC:"intrinsic"},P=["Webkit","webkit","Moz","moz","ms","O","o"];function U(t){var i=t.replace(/[A-Z]/g,(function(t){return"-"+t.toLowerCase()}));return P.some((function(t){return i.startsWith(t+"-")}))?"-".concat(i):i}function A(t,i,n){if(i.startsWith("--"))return i;I||(I=v());var r=I[i];if(!r||n){if(r=i,void 0===t[i]){var e=function(t){return t.charAt(0).toUpperCase()+t.slice(1)}(i),o=function(t,i){for(var n=0;n<P.length;n++){var r=P[n]+i;if(void 0!==t[r])return r}return""}(t,e);void 0!==t[o]&&(r=o)}n||(I[i]=r)}return r}function N(t,i){var n=t.style;for(var r in i)n.setProperty(U(A(n,r)),String(i[r]),"important")}function L(t,i,n,r,e){var o=A(t.style,i,e);if(o){var a=r?n+r:n;t.style.setProperty(U(o),a)}}function M(t,i){for(var n in i)L(t,n,i[n])}function _(t,i){void 0===i&&(i=t.hasAttribute("hidden")),i?t.removeAttribute("hidden"):t.setAttribute("hidden","")}function C(t,i,n){return i in t?Object.defineProperty(t,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[i]=n,t}function F(t,i){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);i&&(r=r.filter((function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),n.push.apply(n,r)}return n}function z(t){for(var i=1;i<arguments.length;i++){var n=null!=arguments[i]?arguments[i]:{};i%2?F(Object(n),!0).forEach((function(i){C(t,i,n[i])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):F(Object(n)).forEach((function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(n,i))}))}return t}var G=/(?:^[#?]?|&)([^=&]+)(?:=([^&]*))?/g;function H(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{return decodeURIComponent(t)}catch(t){return i}}function X(t){var i,n=v();if(!t)return n;for(;i=G.exec(t);){var r=H(i[1],i[1]),e=i[2]?H(i[2].replace(/\+/g," "),i[2]):"";n[r]=e}return n}self.__AMP_LOG=self.__AMP_LOG||{user:null,dev:null,userForEmbed:null};var J=self.__AMP_LOG;function D(t,i){throw new Error("failed to call initLogConstructor")}function q(t){return J.user||(J.user=B()),function(t,i){return i&&i.ownerDocument.defaultView!=t}(J.user.win,t)?J.userForEmbed||(J.userForEmbed=B()):J.user}function B(t){return D()}function $(){return J.dev||(J.dev=D())}function V(t,i,n,r,e,o,a,s,u,l,f){return t}function Y(t,i,n,r,e,o,a,s,u,l,f){return q().assert(t,i,n,r,e,o,a,s,u,l,f)}function W(t,i){return nt(t=function(t){return t.__AMP_TOP||(t.__AMP_TOP=t)}(t),i)}function Z(t,i){return nt(it(tt(t)),i)}function K(t,i){var n=it(tt(t));return ot(n,i)?nt(n,i):null}function Q(t,i){return rt(it(t),i)}function tt(t){return t.nodeType?(n=t,i=(n.ownerDocument||n).defaultView,W(i,"ampdoc")).getAmpDoc(t):t;var i,n}function it(t){var i=tt(t);return i.isSingleDoc()?i.win:i}function nt(t,i){V(ot(t,i));var n=et(t)[i];return n.obj||(V(n.ctor),V(n.context),n.obj=new n.ctor(n.context),V(n.obj),n.context=null,n.resolve&&n.resolve(n.obj)),n.obj}function rt(t,i){var n=et(t)[i];return n?n.promise?n.promise:(nt(t,i),n.promise=Promise.resolve(n.obj)):null}function et(t){var i=t.__AMP_SERVICES;return i||(i=t.__AMP_SERVICES={}),i}function ot(t,i){var n=t.__AMP_SERVICES&&t.__AMP_SERVICES[i];return!(!n||!n.ctor)}var at="__AMP__EXPERIMENT_TOGGLES",st=function(){function t(t){this.G=t,this.K=0,this.Y=0,this.rr=v()}var i=t.prototype;return i.has=function(t){return!!this.rr[t]},i.get=function(t){var i=this.rr[t];if(i)return i.access=++this.Y,i.payload},i.put=function(t,i){this.has(t)||this.K++,this.rr[t]={payload:i,access:this.Y},this.nr()},i.nr=function(){if(!(this.K<=this.G)){var t,i=this.rr,n=this.Y+1;for(var r in i){var e=i[r].access;e<n&&(n=e,t=r)}void 0!==t&&(delete i[t],this.K--)}},t}();function ut(t,i,n){var r=K(t,i);return r?Promise.resolve(r):function(t,i,n,r){var e=Q(t,i);if(e)return e;var a=tt(t);return a.whenExtensionsKnown().then((function(){var t=a.getExtensionVersion(n);return t?W(a.win,"extensions").waitForExtension(n,t):null})).then((function(n){return n?function(t,i){return function(t,i){var n=rt(t,i);if(n)return n;var r,e,a,s,u=et(t);return u[i]=(e=(r=new o).promise,a=r.reject,s=r.resolve,e.catch((function(){})),{obj:null,promise:e,resolve:s,reject:a,context:null,ctor:null}),u[i].promise}(it(t),i)}(t,i):null}))}(t,i,n)}var lt,ft,ht=function(t){return W(t,"batched-xhr")},ct=function(t){return ut(t,"bind","amp-bind")},mt=function(t){return W(t,"xhr")},dt=function(){return self.AMP.config.urls}(),vt=new Set(["c","v","a","ad"]),pt=function(t){return"string"==typeof t?bt(t):t};function bt(t,i){return lt||(lt=self.document.createElement("a"),ft=self.__AMP_URL_CACHE||(self.__AMP_URL_CACHE=new st(100))),function(t,i,n){if(n&&n.has(i))return n.get(i);t.href=i,t.protocol||(t.href=t.href);var r,e={href:t.href,protocol:t.protocol,host:t.host,hostname:t.hostname,port:"0"==t.port?"":t.port,pathname:t.pathname,search:t.search,hash:t.hash,origin:null};"/"!==e.pathname[0]&&(e.pathname="/"+e.pathname),("http:"==e.protocol&&80==e.port||"https:"==e.protocol&&443==e.port)&&(e.port="",e.host=e.hostname),r=t.origin&&"null"!=t.origin?t.origin:"data:"!=e.protocol&&e.host?e.protocol+"//"+e.host:e.href,e.origin=r;var o=e;return n&&n.put(i,o),o}(lt,t,i?null:ft)}function gt(t,i){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(i))}function yt(t){return"https:"==(t=pt(t)).protocol||"localhost"==t.hostname||"127.0.0.1"==t.hostname||(n=".localhost",(r=(i=t.hostname).length-n.length)>=0&&i.indexOf(n,r)==r);var i,n,r}function wt(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"source";return Y(null!=t,"%s %s must be available",i,n),Y(yt(t)||/^\/\//.test(t),'%s %s must start with "https://" or "//" or be relative and served from either https or from localhost. Invalid value: %s',i,n,t),t}function xt(t){return t.startsWith("amp-script:")}function Ot(t){return bt(function(t){if(!function(t){return dt.cdnProxyRegex.test(pt(t).origin)}(t=pt(t)))return t.href;var i=t.pathname.split("/"),n=i[1];Y(vt.has(n),"Unknown path prefix in url %s",t.href);var r=i[2],e="s"==r?"https://"+decodeURIComponent(i[3]):"http://"+decodeURIComponent(r);return Y(e.indexOf(".")>0,"Expected a . in origin %s",e),i.splice(1,"s"==r?3:2),e+i.join("/")+function(t,i){if(!t||"?"==t)return"";var n=new RegExp("[?&]".concat("(amp_(js[^&=]*|gsa|r|kit)|usqp)","\\b[^&]*"),"g"),r=t.replace(n,"").replace(/^[?&]/,"");return r?"?"+r:""}(t.search)+(t.hash||"")}(t)).origin}var jt="i-amphtml-key",St="i-amphtml-ignore",Rt={"AMP-IMG":["src","srcset","layout","width","height"]};Object.freeze({"input":{"type":/(?:image|button)/i}}),Object.freeze({"input":{"type":/(?:button|file|image|password)/i}});var Et,kt=Object.freeze(["form","formaction","formmethod","formtarget","formnovalidate","formenctype"]);function It(t,i,n,r){var e={detail:n};if(Object.assign(e,r),"function"==typeof t.CustomEvent)return new t.CustomEvent(i,e);var o=t.document.createEvent("CustomEvent");return o.initCustomEvent(i,!!e.bubbles,!!e.cancelable,n),o}function Tt(t,i,n,r){return function(t,i,n,r){var e=t,o=n,a=function(t){try{return o(t)}catch(t){var i,n;throw null===(i=(n=self).__AMP_REPORT_ERROR)||void 0===i||i.call(n,t),t}},s=function(){if(void 0!==Et)return Et;Et=!1;try{var t={get capture(){return Et=!0,!1}};self.addEventListener("test-options",null,t),self.removeEventListener("test-options",null,t)}catch(t){}return Et}(),u=!(null==r||!r.capture);return e.addEventListener(i,a,s?r:u),function(){null==e||e.removeEventListener(i,a,s?r:u),o=null,e=null,a=null}}(t,i,n,r)}function Pt(t){return!!t&&"function"==typeof t.getFormData}Object.freeze({"input":kt,"textarea":kt,"select":kt}),Object.freeze({"amp-anim":["controls"],"form":["name"]});var Ut=["GET","POST"],At=[h,function(t){return"[object Object]"===d.call(t)}];function Nt(t,i){var n=z({},i);if(Pt(i.body)){var r=i.body;n.headers["Content-Type"]="multipart/form-data;charset=utf-8",n.body=function(t){for(var i=[],n=t.next();!n.done;n=t.next())i.push(n.value);return i}(r.entries())}return{input:t,init:n}}Ct.KEY="data-key",Ct.IGNORE="data-ignore",Ct.CHECKSUM="data-checksum";var Lt,Mt="_set-dom-mounted",_t=null;function Ct(t,i){_t=[],function(t,i){if(!t)throw new Error("set-dom: You must provide a valid node to update.")}(t&&t.nodeType),9===t.nodeType&&(t=t.documentElement),11===i.nodeType?zt(t,i):Ft(t,i),t[Mt]||(t[Mt]=!0,Jt(t));var n=_t;return _t=null,n}function Ft(t,i){if(t.nodeType===i.nodeType)if(1===t.nodeType){if(e=i,(o=Xt(r=t)&&Xt(e))&&_t.push(r,e),o||Ht(r)===Ht(e)||r.isEqualNode(e))return;if(zt(t,i),t.nodeName===i.nodeName)!function(t,i){var n,r,e,o,a;for(n=t.length;n--;)o=(r=t[n]).namespaceURI,a=r.localName,(e=i.getNamedItemNS(o,a))||t.removeNamedItemNS(o,a);for(n=i.length;n--;)o=(r=i[n]).namespaceURI,a=r.localName,(e=t.getNamedItemNS(o,a))?e.value!==r.value&&(e.value=r.value):(i.removeNamedItemNS(o,a),t.setNamedItemNS(r))}(t.attributes,i.attributes);else{for(var n=i.cloneNode();t.firstChild;)n.appendChild(t.firstChild);t.parentNode.replaceChild(n,t)}}else t.nodeValue!==i.nodeValue&&(t.nodeValue=i.nodeValue);else t.parentNode.replaceChild(i,Dt(t)),Jt(i);var r,e,o}function zt(t,i){for(var n,r,e,o,a,s,u=t.firstChild,l=i.firstChild,f=0;u;)f++,r=Gt(n=u),u=u.nextSibling,r&&(s||(s={}),s[r]=n);for(u=t.firstChild;l;)f--,e=l,l=l.nextSibling,s&&(o=Gt(e))&&(a=s[o])?(delete s[o],a!==u?t.insertBefore(a,u):u=u.nextSibling,Ft(a,e)):u?(n=u,u=u.nextSibling,Gt(n)?(t.insertBefore(e,n),Jt(e)):Ft(n,e)):(t.appendChild(e),Jt(e));for(r in s)f--,t.removeChild(Dt(s[r]));for(;--f>=0;)t.removeChild(Dt(t.lastChild))}function Gt(t){if(1===t.nodeType){var i=t.getAttribute(Ct.KEY)||t.id;return i?"_set-dom-"+i:void 0}}function Ht(t){return t.getAttribute(Ct.CHECKSUM)||NaN}function Xt(t){return null!=t.getAttribute(Ct.IGNORE)}function Jt(t){return qt(t,"mount")}function Dt(t){return qt(t,"dismount")}function qt(t,i){if(Gt(t)){var n=document.createEvent("Event"),r={value:t};n.initEvent(i,!1,!1),Object.defineProperty(n,"target",r),Object.defineProperty(n,"srcElement",r),t.dispatchEvent(n)}for(var e=t.firstChild;e;)e=qt(e,i).nextSibling;return t}function Bt(t){var i=t.ownerDocument||t;return Lt&&Lt.ownerDocument===i||(Lt=i.createElement("div")),$t}function $t(t){return function(t,i){if(g(1===i.length),g(Array.isArray(i)||p(i,"raw")),self.trustedTypes&&self.trustedTypes.createPolicy){var n=self.trustedTypes.createPolicy("static-template#createNode",{createHTML:function(t){return i[0]}});t.innerHTML=n.createHTML("ignored")}else t.innerHTML=i[0];var r=t.firstElementChild;return g(r),g(!r.nextElementSibling),t.removeChild(r),r}(Lt,t)}var Vt=['<amp-list-load-more load-more-button class="amp-visible i-amphtml-default-ui"><button load-more-clickable class=i-amphtml-list-load-more-button><label>See More</label></button></amp-list-load-more>'],Yt=["<amp-list-load-more load-more-loading class=i-amphtml-default-ui><div class=i-amphtml-list-load-more-spinner></div></amp-list-load-more>"],Wt=['<amp-list-load-more load-more-failed class=i-amphtml-default-ui><div class=i-amphtml-list-load-more-message>Unable to Load More</div><button load-more-clickable class="i-amphtml-list-load-more-button i-amphtml-list-load-more-button-has-icon i-amphtml-list-load-more-button-small"><div class=i-amphtml-list-load-more-icon></div><label>Retry</label></button></amp-list-load-more>'],Zt=function(){function t(t){this.P3=t,this.U3=null,this.G3=null,this.H3=null,this.X3=null,this.J3=null,this.q3=null}var i=t.prototype;return i.initializeLoadMore=function(){this.B3(),this.V3(),this.Y3(),this.W3()},i.B3=function(){this.U3=R(this.P3,"load-more-button"),this.U3?this.U3.classList.add("amp-visible"):this.U3=Bt(this.P3)(Vt),this.P3.appendChild(this.U3),M(this.U3,{visibility:"hidden"})},i.V3=function(){this.H3=R(this.P3,"load-more-loading"),this.H3||(this.H3=Bt(this.P3)(Yt)),this.P3.appendChild(this.H3)},i.getLoadMoreButton=function(){return this.U3||this.B3(),this.U3},i.getLoadMoreLoadingElement=function(){return this.H3||this.V3(),this.H3},i.getLoadMoreButtonClickable=function(){if(!this.G3){var t=this.getLoadMoreButton();this.G3=R(t,"load-more-clickable")||t}return this.G3},i.Y3=function(){this.X3=R(this.P3,"load-more-failed"),this.X3||(this.X3=Bt(this.P3)(Wt)),this.P3.appendChild(this.X3)},i.getLoadMoreFailedElement=function(){return this.X3||this.Y3(),this.X3},i.getLoadMoreFailedClickable=function(){if(!this.J3){var t=this.getLoadMoreFailedElement();this.J3=R(t,"load-more-clickable")||t}return this.J3},i.W3=function(){this.q3||(this.q3=R(this.P3,"load-more-end"),this.q3&&this.P3.appendChild(this.q3))},i.getLoadMoreEndElement=function(){return this.q3},i.setLoadMoreEnded=function(){this.getLoadMoreFailedElement().classList.toggle("amp-visible",!1),this.getLoadMoreButton().classList.toggle("amp-visible",!1),this.getLoadMoreLoadingElement().classList.toggle("amp-visible",!1);var t=this.getLoadMoreEndElement();t&&t.classList.toggle("amp-visible",!0)},i.toggleLoadMoreLoading=function(t){t&&this.getLoadMoreFailedElement().classList.toggle("amp-visible",!1);var i=this.getLoadMoreEndElement();i&&i.classList.toggle("amp-visible",!1),this.getLoadMoreButton().classList.toggle("amp-visible",!t),this.getLoadMoreLoadingElement().classList.toggle("amp-visible",t)},i.setLoadMoreFailed=function(){var t=this.getLoadMoreFailedElement(),i=this.getLoadMoreButton();(t||i)&&(t.classList.toggle("amp-visible",!0),i.classList.toggle("amp-visible",!1),this.getLoadMoreLoadingElement().classList.toggle("amp-visible",!1))},i.hideAllLoadMoreElements=function(){this.getLoadMoreButton().classList.toggle("amp-visible",!1),this.getLoadMoreLoadingElement().classList.toggle("amp-visible",!1),this.getLoadMoreFailedElement().classList.toggle("amp-visible",!1),this.getLoadMoreEndElement()&&this.getLoadMoreEndElement().classList.toggle("amp-visible",!1)},t}();function Kt(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.expr,e=void 0===r?".":r,o=n.refresh,a=void 0!==o&&o,s=n.url,u=void 0===s?i.getAttribute("src"):s,l=n.urlReplacement,f=void 0===l?0:l,h=n.xssiPrefix,c=void 0===h?void 0:h;wt(u,i);var m=ht(t.win);return Qt(i,u,f,a).then((function(t){return m.fetchJson(t.xhrUrl,t.fetchOpt)})).then((function(i){return mt(t.win).xssiJson(i,c)})).then((function(t){if(null==t)throw new Error("Response is undefined.");return b(t,e||".")})).catch((function(t){throw q().createError("failed fetching JSON data",t)}))}function Qt(t,i,n,r){var e=function(t){return K(t,"url-replace")}(t);return(n>=1?e.expandUrlAsync(i):Promise.resolve(i)).then((function(i){if(1===n){var o=e.collectDisallowedVarsSync(t);if(o.length>0)throw q().createError("URL variable substitutions in CORS fetches from dynamic URLs (e.g. via amp-bind) require opt-in. "+'Please add data-amp-replace="'.concat(o.join(" "),'" to the ')+"<".concat(t.tagName,"> element. See https://bit.ly/amp-var-subs."))}var a={};return t.hasAttribute("credentials")&&(a.credentials=t.getAttribute("credentials")),r&&(a.cache="reload"),{"xhrUrl":i,"fetchOpt":a}}))}var ti=function(){function t(t,i,n){var r=this;this.ce=nt(t,"timer"),this.fe=i,this.le=n||0,this.ve=-1,this.de=0,this.me=!1,this.pe=function(){r.be()}}var i=t.prototype;return i.isPending=function(){return-1!=this.ve},i.schedule=function(t){var i=t||this.le;this.me&&i<10&&(i=10);var n=Date.now()+i;return(!this.isPending()||n-this.de<-10)&&(this.cancel(),this.de=n,this.ve=this.ce.delay(this.pe,i),!0)},i.be=function(){this.ve=-1,this.de=0,this.me=!0,this.fe(),this.me=!1},i.cancel=function(){this.isPending()&&(this.ce.cancel(this.ve),this.ve=-1)},t}(),ii=function(){function t(t,i,n){this.Le=i,this.uw=n,this.gJ=t}var i=t.prototype;return i.isEnabled=function(){var t=this.Le.getAmpDoc();return!(!t.isSingleDoc()||!t.getRootNode().documentElement.hasAttribute("allow-viewer-render-template"))&&this.Le.hasCapability("viewerRenderTemplate")},i.assertTrustedViewer=function(t){return this.Le.isTrustedViewer().then((function(i){Y(i,"Refused to attempt SSR in untrusted viewer: ",t)}))},i.ssr=function(t,i){var n,r=this,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return e||(n=this.uw.maybeFindTemplate(t)),this.assertTrustedViewer(t).then((function(){return r.Le.sendMessageAwaitResponse("viewerRenderTemplate",r.jJ(i,n,e,o))}))},i.applySsrOrCsrTemplate=function(t,i){var n,r=this;return this.isEnabled()?(Y("string"==typeof i.html,"Skipping template rendering due to failed fetch"),n=this.assertTrustedViewer(t).then((function(){return r.uw.findAndSetHtmlForTemplate(t,i.html)}))):n=h(i)?this.uw.findAndRenderTemplateArray(t,i):this.uw.findAndRenderTemplate(t,i),n},i.jJ=function(t,i,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},e={"type":this.gJ},o="successTemplate",a=n&&n[o]?n[o]:i;a&&(e[o]={"type":"amp-mustache","payload":a.innerHTML});var s="errorTemplate",u=n&&n[s]?n[s]:null;u&&(e[s]={"type":"amp-mustache","payload":u.innerHTML}),r&&Object.assign(e,r);var l={"originalRequest":Nt(t.xhrUrl,t.fetchOpt),"ampComponent":e};return l},t}(),ni="amp-list",ri="amp-state:",ei=function(t){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&s(t,i)}(m,t);var i,r,c=(i=m,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,n=u(i);if(r){var e=u(this).constructor;t=Reflect.construct(n,arguments,e)}else t=n.apply(this,arguments);return l(this,t)});function m(t){var i;return(i=c.call(this,t)).aw=null,i.eo=null,i.CJ=!1,i.Z3=new ti(i.win,(function(){return i.K3()})),i.Q3=null,i.t6=null,i.uw=null,i.y3=!1,i.i6=!1,i.n6=null,i.r6=null,i.e6=null,i.o6=!1,i.a6=null,i.s6=null,i.u6=!1,i.l6=null,i.registerAction("refresh",(function(){if(i.y3)return i.f6(),i.h6({refresh:!0})})),i.registerAction("changeToLayoutContainer",(function(){return i.c6()})),i.m6=null,i.Oe=null,i.pG=null,i}var d=m.prototype;return d.isLayoutSupported=function(t){if(t===T.CONTAINER){var i=this.element.ownerDocument,r=i&&function(t){return function(t,i){var n=i.documentElement;return["⚡4email","amp4email"].some((function(t){return n.hasAttribute(t)}))}(0,t)}(i),e=this.getPlaceholder()||this.element.hasAttribute("diffable");return r?(e||q().warn(ni,"amp-list[layout=container] should have a placeholder to establish an initial size. See https://go.amp.dev/c/amp-list/#placeholder-and-fallback. %s",this.element),this.i6=!0):(Y(("amp-list-layout-container",o=function(t){var i,r,e,o,a;if(t[at])return t[at];t[at]=v();var s=t[at];g(s);var u,l=z(z({},null!==(i=t.AMP_CONFIG)&&void 0!==i?i:{}),null!==(r=t.AMP_EXP)&&void 0!==r?r:(u=(null===(e=t.__AMP_EXP)||void 0===e?void 0:e.textContent)||"{}",JSON.parse(u)));for(var f in l){var c=l[f];"number"==typeof c&&c>=0&&c<=1&&(s[f]=Math.random()<c)}var m=null===(o=t.AMP_CONFIG)||void 0===o?void 0:o["allow-doc-opt-in"];if(h(m)&&m.length){var d=t.document.head.querySelector('meta[name="amp-experiments-opt-in"]');if(d)for(var p,b,y=n((null===(p=d.getAttribute("content"))||void 0===p?void 0:p.split(","))||[],!0);!(b=y()).done;){var w=b.value;m.includes(w)&&(s[w]=!0)}}Object.assign(s,function(t){var i,r="";try{var e;"localStorage"in t&&(r=null!==(e=t.localStorage.getItem("amp-experiment-toggles"))&&void 0!==e?e:"")}catch(t){$().warn("EXPERIMENTS","Failed to retrieve experiments from localStorage.")}for(var o,a=(null===(i=r)||void 0===i?void 0:i.split(/\s*,\s*/g))||[],s=v(),u=n(a,!0);!(o=u()).done;){var l=o.value;l&&("-"==l[0]?s[l.substr(1)]=!1:s[l]=!0)}return s}(t));var x=null===(a=t.AMP_CONFIG)||void 0===a?void 0:a["allow-url-opt-in"];if(h(x)&&x.length)for(var O,j=X(t.location.originalHash||t.location.hash),S=n(x,!0);!(O=S()).done;){var R=O.value,E=j["e-".concat(R)];"1"==E&&(s[R]=!0),"0"==E&&(s[R]=!1)}return s}(this.win),!!o["amp-list-layout-container"]),'Experiment "amp-list-layout-container" is not turned on.'),Y(e,"amp-list[layout=container] should have a placeholder to establish an initial size. See https://go.amp.dev/c/amp-list/#placeholder-and-fallback. %s",this.element),this.i6=!0)}var o;return function(t){return t==T.FIXED||t==T.FIXED_HEIGHT||t==T.RESPONSIVE||t==T.FILL||t==T.FLEX_ITEM||t==T.FLUID||t==T.INTRINSIC}(t)},d.buildCallback=function(){var t=this;this.uw=Z(this.element,"templates"),this.Oe=K(this.element,"action"),this.pG=Z(this.element,"owners"),this.Oe.addToAllowlist("AMP-LIST",["changeToLayoutContainer","refresh"],["email"]),this.eo=this.getViewport();var i=function(t){return Z(t,"viewer")}(this.getAmpDoc());this.m6=new ii(ni,i,this.uw),this.o6=this.element.hasAttribute("load-more"),Y(!(this.o6&&this.i6),"%s initialized with layout=container does not support infinite scrolling with [load-more]. %s",ni,this.element),this.n6=this.element.getAttribute("src"),this.element.hasAttribute("diffable")&&(this.r6=this.d6(),this.r6?this.aw=this.r6:q().warn(ni,"Could not find child div for diffing.",this.element)),this.aw||(this.aw=this.FJ(),this.element.appendChild(this.aw)),this.element.hasAttribute("auto-resize")&&q().warn(ni,"auto-resize attribute is deprecated and its behavior is disabled. This feature will be relaunched under a new name soon. Please see https://github.com/ampproject/amphtml/issues/18849"),Ct.KEY=jt,Ct.IGNORE=St,ct(this.element).then((function(i){t.e6=i}))},d.reconstructWhenReparented=function(){return!1},d.layoutCallback=function(){var t=this;this.y3=!0;var i=this.getPlaceholder();return i?this.v6(i):this.r6&&this.v6(this.aw),this.eo.onResize((function(){t.p6()})),this.o6&&this.b6(),this.h6()},d.d6=function(){var t=this.element.hasAttribute("single-item")?"> div":"> div[role=list]";return t+=":not([placeholder]):not([fallback]):not([fetch-error])",S(this.element,t)},d.b6=function(){var t=this;return this.mutateElement((function(){t.g6().initializeLoadMore();var i=t.getOverflowElement();i&&_(i,!1),t.element.warnOnMissingOverflow=!1})).then((function(){t.y6(),Tt(t.g6().getLoadMoreFailedClickable(),"click",(function(){return t.w6(!0,!0)})),Tt(t.g6().getLoadMoreButtonClickable(),"click",(function(){return t.w6(!1,!0)}))}))},d.p6=function(){if(!this.o6)return this.v6(this.aw);this.x6(this.aw)},d.g6=function(){return this.a6||(this.a6=new Zt(this.element)),this.a6},d.y6=function(){var t,i,n=this;return this.measureMutateElement((function(){t=n.g6().getLoadMoreButton().offsetHeight,i=n.element.offsetHeight}),(function(){var r;M(n.aw,{"max-height":"calc(100% - ".concat((r=t,"".concat(r,"px")),")")}),n.element.applySize(i+t)}))},d.O6=function(t){return t.startsWith(ri)},d.j6=function(t){var i=this;return ct(this.element).then((function(n){Y(n,'"amp-state:" URLs require amp-bind to be installed.'),Y(!i.m6.isEnabled(),'[amp-list]: "amp-state" URIs cannot be used in SSR mode.');var r=t.slice(ri.length);return n.getStateAsync(r).catch((function(t){var i=r.split(".")[0];throw q().error(ni,"'amp-state' element with id '".concat(i,"' was not found.")),t}))})).then((function(i){return Y(void 0!==i,"[amp-list] No data was found at provided uri: ".concat(t)),i}))},d.S6=function(t){var i=this;return e().then((function(){return Y(!i.m6.isEnabled(),'[amp-list]: "amp-script" URIs cannot be used in SSR mode.'),(n=i.element,ut(n,"amp-script","amp-script")).then((function(i){return i.fetch(t)}));var n})).then((function(i){return Y("object"===a(i),"[amp-list] ".concat(t," must return json, but instead returned: ").concat(a(i))),i}))},d.mutatedAttributesCallback=function(t){var i=this;$().info(ni,"mutate:",this.element,t);var n=t.src;void 0!==n&&("string"==typeof n?this.y3&&(this.f6(),this.h6()):"object"===a(n)?function(t){i.element.setAttribute("src",""),Y(!i.m6.isEnabled(),'[amp-list] "[src]" may not be bound in SSR mode.');var n=h(t)?t:[t];i.f6(!1),i.qL(n,!1)}(n):this.user().error(ni,'Unexpected "src" type: '+n)),t["is-layout-container"]&&this.c6()},d.FJ=function(){var t,i=this.win.document.createElement("div");return this.R6(i,"list"),this.o6||this.i6||((t=i).classList.add("i-amphtml-fill-content"),t.classList.add("i-amphtml-replaced-content")),i},d.E6=function(t,i){var n=this;t.forEach((function(t){t.hasAttribute("role")||n.R6(t,"listitem"),i.appendChild(t)}))},d.R6=function(t,i){this.element.hasAttribute("single-item")||t.setAttribute("role",i)},d.k6=function(t){(t||this.CJ)&&(this.toggleFallback(t),this.CJ=t)},d.f6=function(){var t=this,i=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(i&&this.element.hasAttribute("reset-on-refresh")||"always"===this.element.getAttribute("reset-on-refresh")){var n=function(){t.togglePlaceholder(!0);var i=t.element.hasAttribute("reset-on-refresh");if(t.toggleLoading(!0,i),t.k6(!1),t.e6){var n=f(t.aw.children);t.e6.rescan([],n,{"fast":!0,"update":!1})}t.pG.scheduleUnlayout(t.element,t.aw),E(t.aw)};if(!this.o6&&this.i6)return void this.I6(n);this.mutateElement((function(){n(),t.o6&&t.g6().hideAllLoadMoreElements()}))}},d.T6=function(t){var i=this.element.getAttribute("items")||"items",n=t;return"."!=i&&(n=b(t,i)),Y(void 0!==n,'Response must contain an array or object at "%s". %s',i,this.element),this.element.hasAttribute("single-item")&&(h(n)?q().warn(ni,'Expected response to contain a non-array Object due to "single-item" attribute.',this.element):n=[n]),n=q().assertArray(n),this.element.hasAttribute("max-items")&&(n=this.P6(n)),n},d.U6=function(t){var i=t?It(this.win,"".concat(ni,".error"),{"response":t.response}):null;this.Oe.trigger(this.element,"fetch-error",i,1)},d.h6=function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=i.append,r=void 0!==n&&n,o=i.refresh,a=void 0!==o&&o,s=this.element.getAttribute("src");return s?(this.m6.isEnabled()?this.A6(a):(this.O6(s)?this.j6(s):xt(s)?this.S6(s):this.N6(a)).then((function(i){if(s===t.element.getAttribute("src")){var n=t.T6(i);return t.o6&&t.L6(i),t.qL(n,r,i).then((function(){return t.M6()}))}}))).catch((function(i){if(r)throw i;throw t.U6(i),t._6(),i})):e()},d.P6=function(t){var i=parseInt(this.element.getAttribute("max-items"),10);return i<t.length&&(t=t.slice(0,i)),t},d.L6=function(t){var i=this.element.getAttribute("load-more-bookmark")||"load-more-src";this.s6=b(t,i)},d.A6=function(t){var i,n=this,r=this.element.getAttribute("src");return Qt(this.element,r,this.$L(),t).then((function(t){var r,e,o;(i=t).xhrUrl=(r=n.win,e=i.xhrUrl,o=i.fetchOpt,g("string"==typeof e),!1!==o.ampCors&&(e=function(t,i){return function(t){Y(!("__amp_source_origin"in X(bt(t).search)),"Source origin is not allowed in %s",t)}(i),function(t,i,n,r){return function(t,i,n){if(!i)return t;var r=t.split("#",2),e=r[0].split("?",2);return e[0]+(e[1]?"?".concat(e[1],"&").concat(i):"?".concat(i))+(r[1]?"#".concat(r[1]):"")}(t,gt("__amp_source_origin",n))}(i,0,Ot(t.location.href))}(r,e)),e),i.fetchOpt=function(t,i,n){n=n||{};var r=function(t){return t.origin||bt(t.location.href).origin}(t);return r==bt(i).origin&&(n.headers=n.headers||{},n.headers["AMP-Same-Origin"]="true"),n}(n.win,i.xhrUrl,i.fetchOpt),function(t){var i=function(t,i){var n,r=t||{},e=r.credentials;return g(void 0===e||"include"==e||"omit"==e),r.method=void 0===(n=r.method)?"GET":(n=n.toUpperCase(),g(Ut.includes(n)),n),r.headers=r.headers||{},r.headers.Accept=i,g(null!==r.body),r}(t,"application/json");if("POST"==i.method&&!Pt(i.body)){g(At.some((function(t){return t(i.body)}))),i.headers["Content-Type"]=i.headers["Content-Type"]||"text/plain;charset=utf-8";var n=i.headers["Content-Type"];i.body="application/x-www-form-urlencoded"===n?function(t){var i,n=[];for(var r in t){var e=t[r];if(null!=e){e=h(i=e)?i:[i];for(var o=0;o<e.length;o++)n.push(gt(r,e[o]))}}return n.join("&")}(i.body):JSON.stringify(i.body)}}(t.fetchOpt);var a={"ampListAttributes":{"items":n.element.getAttribute("items")||"items","singleItem":n.element.hasAttribute("single-item"),"maxItems":n.element.getAttribute("max-items")}};return n.m6.ssr(n.element,i,null,a)})).then((function(t){Y(t,"Failed fetching JSON data: XHR Failed fetching "+"(".concat(n.C6(i),"): received no response."));var r=t.init;if(r){var e=r.status;if(e>=300)throw q().createError("Failed fetching JSON data (".concat(n.C6(i),")")+": HTTP error",e)}return Y("string"==typeof t.html,"Failed fetching JSON data: XHR Failed fetching "+"(".concat(n.C6(i),"): Expected response with ")+"format {html: <string>}. Received: ",t),i.fetchOpt.responseType="application/json",t}),(function(t){throw q().createError("Failed fetching JSON data: XHR Failed fetching "+"(".concat(n.C6(i),")"),t)})).then((function(t){if(r===n.element.getAttribute("src"))return n.qL(t,!1)}))},d.C6=function(t){var i,n=(i=this.element,K(i,"url")).parse(t.xhrUrl);return"".concat(n.origin,"/...")},d.qL=function(t,i,n){var r=new o,e=r.promise,a=r.reject,s=r.resolve;return this.Q3||this.Z3.schedule(),this.Q3={data:t,resolver:s,rejecter:a,append:i,payload:n},this.t6&&i&&(this.Q3.payload=n||{}),e},d.K3=function(){var t=this,i=this.Q3;q().fine(ni,"Rendering list",this.element,"with data",i.data),V(i&&i.data);var n=function(){t.Q3!==i?t.Z3.schedule(1):(t.t6=t.Q3.data,t.Q3=null)},r=this.m6.isEnabled(),e=this.m6.applySsrOrCsrTemplate(this.element,i.data).then((function(n){return t.F6(n,i.append)})).then((function(n){return t.Ch(n,i.append)}));if(!r){var o=i.payload;e=e.then((function(){return t.z6(o)}))}e.then((function(){n(),i.resolver()}),(function(){n(),i.rejecter()}))},d.z6=function(t){if(this.o6){var i=[];return i.push(this.G6(this.g6().getLoadMoreButton(),t)),i.push(this.G6(this.g6().getLoadMoreEndElement(),t)),Promise.all(i)}return e()},d.G6=function(t,i){var n=this;return t&&this.uw.hasTemplate(t)?this.uw.findAndRenderTemplate(t,i).then((function(i){return n.mutateElement((function(){E(t),t.appendChild(i)}))})):e()},d.F6=function(t,i){var n=this,r=h(t)?t:[t],e=this.element.getAttribute("binding");if("no"===e)return Promise.resolve(r);if(!r.some((function(t){return t.hasAttribute("i-amphtml-binding")||!!t.querySelector("[i-amphtml-binding]")})))return Promise.resolve(r);e||q().warn(ni,'Missing "binding" attribute. Using binding="refresh" is recommended for performance.');var o=function(t){var e=i?[]:[n.aw];return t.rescan(r,e,{"fast":!0,"update":!0}).then((function(){return r}),(function(){return r}))};return e&&e.startsWith("refresh")?this.e6&&this.e6.signals().get("FIRST_MUTATE")?o(this.e6):(this.element.hasAttribute("diffable")||this.H6(r,[]),Promise.resolve(r)):ct(this.element).then((function(t){return t?o(t):Promise.resolve(r)}))},d.H6=function(t,i){var n=this.element.getAttribute("binding");n&&n.startsWith("refresh")&&ct(this.element).then((function(r){if(r){var e="refresh-evaluate"==n&&"evaluate";r.rescan(t,i,{"fast":!0,"update":e})}}))},d.Ch=function(t){var i=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.aw,e=function(){i.X6(),i.element.hasAttribute("diffable")?(i.J6(r,t),i.H6([r],[r])):(n||(i.pG.scheduleUnlayout(i.element,r),E(r)),i.E6(t,r)),i.element.closest("amp-story-page-attachment")&&i.aw.querySelectorAll("amp-img").forEach((function(t){t.getImpl().then((function(t){return t.layoutCallback()}))}));var e=It(i.win,w,null,{bubbles:!0});return i.aw.dispatchEvent(e),i.element.getResources().getResourceForElement(i.element).resetPendingChangeSize(),i.p6()};return!this.o6&&this.i6?this.I6((function(){(e()||Promise.resolve(!0)).then((function(t){return t?i.D6():null}))})):this.mutateElement(e)},d.J6=function(t,i){var n=this.FJ();this.E6(i,n),this.r6&&this.q6(t);for(var r=Ct(t,n),e=0;e<r.length;e+=2){var o=r[e],a=r[e+1];this.B6(o,a)}},d.q6=function(t){var i=-1;f(t.querySelectorAll(".i-amphtml-element")).forEach((function(t){!function(t,n){var r=t.tagName.startsWith("AMP-"),e=t.hasAttribute("i-amphtml-binding");!e&&Rt[t.tagName]?t.setAttribute(St,""):(e||r)&&(t.hasAttribute(jt)||t.setAttribute(jt,String(i--)))}(t)}))},d.B6=function(t,i){V(t.nodeName==i.nodeName);var n=Rt[t.nodeName];if(n)if(n.some((function(n){return t.getAttribute(n)!==i.getAttribute(n)})))t.parentElement.replaceChild(i,t);else{for(var r=0;r<i.classList.length;r++)t.classList.add(i.classList[r]);for(var e=0;e<t.classList.length;e++){var o=t.classList[e];o.startsWith("i-amphtml-")||i.classList.contains(o)||t.classList.remove(o)}if(i.hasAttribute("style")){var a=i.getAttribute("style");t.setAttribute("style","".concat(t.getAttribute("style")||"",";").concat(a))}}},d.I6=function(t){var i,n=this;return!this.i6||this.o6?($().error(ni,"%s initialized with layout=container does not support infinite scrolling with [load-more]. %s",this.element),e()):this.measureMutateElement((function(){i=n.element.offsetHeight}),(function(){return N(n.element,{"height":"".concat(i,"px"),"overflow":"hidden"}),t()}))},d.D6=function(){V(this.i6&&!this.o6),N(this.element,{"height":"","overflow":""})},d.v6=function(t){var i=this;return this.element.getAttribute("layout")!=T.CONTAINER||this.i6?this.measureElement((function(){var n=t.scrollHeight;return!(n>i.element.offsetHeight)||i.attemptChangeHeight(n).then((function(){return!0}),(function(){return!1}))})):Promise.resolve(!0)},d.x6=function(t){var i=this.s6?this.g6().getLoadMoreButton():this.g6().getLoadMoreEndElement();this.V6(i,t)},d.V6=function(t,i){var n=this;this.element.getAttribute("layout")!=T.CONTAINER&&this.measureElement((function(){var r=i.scrollHeight,e=n.element.offsetHeight,o=t?t.offsetHeight:0;r+o>e&&n.attemptChangeHeight(r+o).then((function(){n.u6=!1,"auto"===n.element.getAttribute("load-more")&&n.Y6(),M(n.aw,{"max-height":""})})).catch((function(){n.u6=!0,n.y6()}))}))},d.W6=function(t){var i=function(t){if("nodisplay"===(i=t)||"fixed"===i||"fixed-height"===i||"responsive"===i||"container"===i||"fill"===i||"flex-item"===i||"fluid"===i||"intrinsic"===i)return t;var i}(t),n=function(t){return"i-amphtml-layout-"+t}(V(i));this.element.classList.remove(n,"i-amphtml-layout-size-defined"),[T.FIXED,T.FLEX_ITEM,T.FLUID,T.INTRINSIC,T.RESPONSIVE].includes(i)?M(this.element,{width:"",height:""}):i==T.FIXED_HEIGHT&&M(this.element,{height:""}),this.element.applySize()},d.c6=function(){var t=this;if(this.i6)return q().warn(ni,"[is-layout-container] and changeToLayoutContainer are ineffective when an amp-list initially sets layout=container",this.element),e();var i=this.element.getAttribute("i-amphtml-layout");return i==T.CONTAINER?e():this.mutateElement((function(){t.W6(i),t.aw.classList.remove("i-amphtml-fill-content","i-amphtml-replaced-content");var n=t.getOverflowElement();n&&_(n,!1),t.element.setAttribute("layout","container"),t.element.setAttribute("i-amphtml-layout","container"),t.element.classList.add("i-amphtml-layout-container")}))},d.M6=function(){return this.o6?this.Z6():e()},d.Z6=function(){var t=this;return this.s6?("auto"===this.element.getAttribute("load-more")&&this.K6(),this.mutateElement((function(){t.g6().toggleLoadMoreLoading(!1),M(t.g6().getLoadMoreButton(),{visibility:""})}))):this.mutateElement((function(){return t.g6().setLoadMoreEnded()}))},d.w6=function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.s6)this.element.setAttribute("src",this.s6),this.s6=null;else if(!i)return e();var r=this.aw,o=this.Q6(r);return this.mutateElement((function(){t.g6().toggleLoadMoreLoading(!0)})),this.h6({append:!0}).then((function(){return t.mutateElement((function(){t.s6?(t.g6().toggleLoadMoreLoading(!1),o&&n&&k(o)):t.g6().setLoadMoreEnded()}))})).then((function(){t.x6(t.aw)})).catch((function(i){t.U6(i),t.t7()}))},d.t7=function(){var t=this;this.mutateElement((function(){return t.g6().setLoadMoreFailed()})).then((function(){t.V6(t.g6().getLoadMoreFailedElement(),t.aw)}))},d.je=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return Kt(this.getAmpDoc(),this.element,{expr:".",urlReplacement:this.$L(),refresh:t,xssiPrefix:this.element.getAttribute("xssi-prefix")||void 0})},d.K6=function(){var t=this;this.l6||(this.l6=this.eo.onChanged((function(){return t.Y6()})))},d.Y6=function(){var t=this;if(!this.u6){var i=this.aw.lastChild||this.aw;this.eo.getClientRectAsync(i).then((function(i){var n=t.eo.getHeight();if(i.bottom>0&&3*n>i.bottom)return t.w6()}))}},d.N6=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.je(t)},d.$L=function(){var t=this.element.getAttribute("src"),i=1;return t!=this.n6&&Ot(t)!=Ot(this.getAmpDoc().win.location)||(i=2),i},d.X6=function(){this.element.classList.remove("i-amphtml-list-fetch-error"),this.toggleLoading(!1),this.getFallback()&&this.k6(!1),this.togglePlaceholder(!1)},d._6=function(){this.element.classList.add("i-amphtml-list-fetch-error"),R(this.element,"fetch-error")&&this.v6(this.element),this.toggleLoading(!1),this.getFallback()&&(this.k6(!0),this.togglePlaceholder(!1))},d.Q6=function(t){var i,n,r=(n='button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"]), audio[controls], video[controls], [contenteditable]:not([contenteditable="false"])',x(i=t)?i.querySelectorAll(O(n,":scope")):j(i,n));return r?r[r.length-1]:null},m}(t.BaseElement);t.registerElement(ni,ei,'amp-list.i-amphtml-list-fetch-error [fetch-error],amp-list[load-more] [load-more-button].amp-visible,amp-list[load-more] [load-more-end].amp-visible,amp-list[load-more] [load-more-failed].amp-visible,amp-list[load-more] [load-more-loading].amp-visible{display:block}amp-list[load-more] [load-more-button].i-amphtml-default-ui,amp-list[load-more] [load-more-failed].i-amphtml-default-ui,amp-list[load-more] [load-more-loading].i-amphtml-default-ui{height:80px;padding:12px 0px;box-sizing:border-box}.i-amphtml-list-load-more-button,amp-list[load-more] [load-more-button].i-amphtml-default-ui,amp-list[load-more] [load-more-failed].i-amphtml-default-ui,amp-list[load-more] [load-more-loading].i-amphtml-default-ui{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;font-weight:700;font-size:14px;text-transform:uppercase;letter-spacing:.1em;color:#333;text-align:center}amp-list[load-more] [load-more-loading].i-amphtml-default-ui .i-amphtml-list-load-more-spinner{display:inline-block;width:40px;height:40px;margin:8px 0px;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><defs><linearGradient id="a"><stop stop-color="%23333" stop-opacity=".75"/><stop offset="100%" stop-color="%23333" stop-opacity="0"/></linearGradient></defs><path fill="none" stroke="url(%23a)" stroke-width="1.725" d="M11 4.4A18 18 0 1 0 38 20"/></svg>\');animation:amp-list-load-more-spinner 1000ms linear infinite}@keyframes amp-list-load-more-spinner{0%{transform:rotate(0deg)}to{transform:rotate(360deg)}}.i-amphtml-list-load-more-button{border:none;display:inline-block;background-color:rgba(51,51,51,.75);color:#fff;margin:4px 0px;padding:0px 32px;box-sizing:border-box;height:48px;border-radius:24px}[load-more] div[role=list]{overflow-y:hidden}.i-amphtml-list-load-more-button,.i-amphtml-list-load-more-button label,.i-amphtml-list-load-more-icon{cursor:pointer}.i-amphtml-list-load-more-button:hover{background-color:#333}.i-amphtml-list-load-more-button.i-amphtml-list-load-more-button-small{margin:0px;padding:4px 16px;height:32px}.i-amphtml-list-load-more-button label{display:inline-block;vertical-align:middle;line-height:24px}amp-list[load-more] [load-more-failed].i-amphtml-default-ui .i-amphtml-list-load-more-message{line-height:24px}amp-list[load-more] [load-more-failed].i-amphtml-default-ui .i-amphtml-list-load-more-icon{height:24px;width:24px;display:inline-block;vertical-align:middle;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path fill="%23fff" d="M17.65 6.35A7.96 7.96 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"/></svg>\')}\n/*# sourceURL=/extensions/amp-list/0.1/amp-list.css*/')}();
/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */}});
//# sourceMappingURL=amp-list-0.1.js.map