#!/usr/bin/env python3
"""
Script untuk memperbaiki struktur UI setelah penghapusan tag AMP
"""

import re
from pathlib import Path

def fix_html_structure():
    """Perbaiki struktur HTML yang rusak"""
    
    # Baca file yang perlu diperbaiki
    fixed_file = Path("index_fixed.html")
    with open(fixed_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 Memperbaiki struktur HTML...")
    
    # Perbaiki struktur HTML
    content = fix_html_head(content)
    content = fix_html_body(content)
    content = fix_amp_remnants(content)
    content = fix_duplicate_elements(content)
    content = fix_broken_tags(content)
    
    # Simpan file yang sudah diperbaiki
    output_file = Path("index_ui_fixed.html")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ UI structure fixed: {output_file}")
    return content

def fix_html_head(content):
    """Perbaiki struktur head HTML"""
    
    # Buat head yang proper
    proper_head = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>RTP Gengtoto Paling Gacor</title>
    <meta name="description" content="RTP Gengtoto Paling Gacor">
    <meta name="googlebot" content="noindex">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/favicon.png.03f868b8e3.png">
    <link rel="shortcut icon" href="https://static.hokibagus.club/gengtoto/rtpslot/gengtoto_fav_icon.png" sizes="16x16">
    
    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json">
    <link rel="canonical" href="http://localhost">
    
    <!-- Preconnect untuk performance -->
    <link rel="preconnect" href="https://statics.hokibagus.club">
    <link rel="preconnect" href="https://cdn.areabermain.club">
    <link rel="preconnect" href="https://rtpslotgeng414.com">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="assets/css2.5af26c8529.48">
</head>'''
    
    # Ganti head yang rusak
    content = re.sub(r'<!DOCTYPE html>.*?</head>', proper_head, content, flags=re.DOTALL)
    
    print("🔧 Fixed HTML head structure")
    return content

def fix_html_body(content):
    """Perbaiki struktur body HTML"""
    
    # Hapus atribut AMP dari body
    content = re.sub(
        r'<body[^>]*class="rtpslot[^"]*"[^>]*>',
        '<body class="rtpslot">',
        content
    )
    
    print("🔧 Fixed body attributes")
    return content

def fix_amp_remnants(content):
    """Hapus sisa-sisa tag AMP yang masih ada"""
    
    # Hapus install-serviceworker
    content = re.sub(r'<install-serviceworker[^>]*>.*?</install-serviceworker>', '', content, flags=re.DOTALL)
    
    # Hapus atribut AMP dari elemen
    amp_attributes = [
        r'\s+i-amphtml-[^=]*="[^"]*"',
        r'\s+i-amphtml-[^=]*',
        r'\s+class="[^"]*i-amphtml[^"]*"',
        r'\s+layout="[^"]*"',
        r'\s+on="[^"]*"',
        r'\s+binding="[^"]*"',
        r'\s+single-item=""',
        r'\s+items="[^"]*"',
        r'\s+side="[^"]*"',
        r'\s+hidden=""',
        r'\s+tabindex="-1"',
        r'\s+role="menu"'
    ]
    
    for attr in amp_attributes:
        content = re.sub(attr, '', content)
    
    # Ganti tag AMP dengan tag HTML standar
    content = re.sub(r'<sidebar', '<div', content)
    content = re.sub(r'</sidebar>', '</div>', content)
    content = re.sub(r'<list', '<div', content)
    content = re.sub(r'</list>', '</div>', content)
    content = re.sub(r'<nested-menu', '<div', content)
    content = re.sub(r'</nested-menu>', '</div>', content)
    
    print("🔧 Removed AMP remnants")
    return content

def fix_duplicate_elements(content):
    """Perbaiki elemen yang duplikat"""
    
    # Perbaiki duplikasi img dalam logo
    logo_pattern = r'<img[^>]*src="https://statics\.hokibagus\.club[^"]*"[^>]*>\s*<img[^>]*src="assets/gengtoto[^"]*"[^>]*></img>'
    
    def fix_logo(match):
        # Ambil img yang kedua (local asset)
        local_img = re.search(r'<img[^>]*src="assets/gengtoto[^"]*"[^>]*>', match.group(0))
        if local_img:
            img_tag = local_img.group(0)
            # Hapus closing tag yang salah
            img_tag = img_tag.replace('></img>', '>')
            # Tambahkan atribut yang diperlukan
            if 'width=' not in img_tag:
                img_tag = img_tag.replace('>', ' width="100" height="33">')
            return img_tag
        return match.group(0)
    
    content = re.sub(logo_pattern, fix_logo, content)
    
    print("🔧 Fixed duplicate elements")
    return content

def fix_broken_tags(content):
    """Perbaiki tag yang rusak"""
    
    # Perbaiki img tags yang rusak
    content = re.sub(r'<img([^>]*)></img>', r'<img\1>', content)
    
    # Perbaiki button dengan atribut on
    content = re.sub(r'<button([^>]*)on="tap:sidebar1"([^>]*)>', r'<button\1\2 onclick="toggleSidebar()">', content)
    
    # Perbaiki div dengan atribut nested
    content = re.sub(r'nested-submenu-open=""', 'class="submenu-toggle"', content)
    content = re.sub(r'nested-submenu=""', 'class="submenu"', content)
    content = re.sub(r'nested-submenu-close=""', 'class="submenu-back"', content)
    
    # Hapus atribut style yang berlebihan
    content = re.sub(r'style="[^"]*--loader-delay-offset[^"]*"', '', content)
    
    print("🔧 Fixed broken tags")
    return content

def create_enhanced_css():
    """Buat CSS tambahan untuk memperbaiki UI"""
    
    css_content = '''
/* UI Fixes untuk struktur yang diperbaiki */

/* Logo fixes */
.flex-item-center img {
    max-width: 100px;
    height: 33px;
    width: auto;
}

/* Sidebar fixes */
.side-bar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: #1a1a1a;
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.side-bar.open {
    right: 0;
}

/* Menu fixes */
.menu-side {
    display: block;
    padding: 15px 20px;
    color: #FAF0D7;
    text-decoration: none;
    border-bottom: 1px solid #333;
    transition: background-color 0.2s ease;
}

.menu-side:hover {
    background-color: #333;
}

/* Submenu fixes */
.submenu {
    display: none;
    background: #2a2a2a;
}

.submenu-toggle {
    cursor: pointer;
    position: relative;
}

.submenu-toggle:after {
    content: "▶";
    position: absolute;
    right: 20px;
    transition: transform 0.2s ease;
}

.submenu-toggle.active:after {
    transform: rotate(90deg);
}

.submenu-toggle.active + .submenu {
    display: block;
}

.submenu-back {
    background: #333;
    cursor: pointer;
}

/* Button fixes */
.side {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .side-bar {
        width: 250px;
        right: -250px;
    }
    
    .flex-item-center img {
        max-width: 80px;
        height: 26px;
    }
}

/* Animation fixes */
.rtpslot {
    animation: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Loading fixes */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}
'''
    
    # Append ke CSS yang ada
    css_file = Path("css/ui-fixes.css")
    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(css_content)
    
    print("🎨 Created UI fixes CSS")

def create_enhanced_js():
    """Buat JavaScript tambahan untuk functionality"""
    
    js_content = '''
// UI Fixes JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 UI Fixes loaded');
    
    // Initialize sidebar
    initializeSidebar();
    
    // Initialize submenu
    initializeSubmenu();
    
    // Fix broken elements
    fixBrokenElements();
});

function initializeSidebar() {
    const sidebarToggle = document.querySelector('.side');
    const sidebar = document.querySelector('.side-bar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleSidebar();
        });
        
        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                closeSidebar();
            }
        });
        
        console.log('📱 Sidebar initialized');
    }
}

function toggleSidebar() {
    const sidebar = document.querySelector('.side-bar');
    if (sidebar) {
        sidebar.classList.toggle('open');
    }
}

function closeSidebar() {
    const sidebar = document.querySelector('.side-bar');
    if (sidebar) {
        sidebar.classList.remove('open');
    }
}

function initializeSubmenu() {
    const submenuToggles = document.querySelectorAll('.submenu-toggle');
    
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            this.classList.toggle('active');
            const submenu = this.nextElementSibling;
            if (submenu && submenu.classList.contains('submenu')) {
                submenu.style.display = submenu.style.display === 'block' ? 'none' : 'block';
            }
        });
    });
    
    // Handle back buttons
    const backButtons = document.querySelectorAll('.submenu-back');
    backButtons.forEach(back => {
        back.addEventListener('click', function() {
            const submenu = this.closest('.submenu');
            const toggle = submenu ? submenu.previousElementSibling : null;
            
            if (submenu) submenu.style.display = 'none';
            if (toggle) toggle.classList.remove('active');
        });
    });
    
    console.log('📋 Submenu initialized');
}

function fixBrokenElements() {
    // Fix images without proper attributes
    const images = document.querySelectorAll('img:not([alt])');
    images.forEach(img => {
        if (!img.alt) {
            img.alt = 'Image';
        }
    });
    
    // Fix links without proper attributes
    const links = document.querySelectorAll('a[href=""]');
    links.forEach(link => {
        link.href = '#';
        link.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });
    
    console.log('🔧 Fixed broken elements');
}

// Global functions
window.toggleSidebar = toggleSidebar;
window.closeSidebar = closeSidebar;
'''
    
    # Simpan JavaScript
    js_file = Path("js/ui-fixes.js")
    with open(js_file, 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    print("⚡ Created UI fixes JavaScript")

def main():
    """Main function"""
    print("🔧 Fixing UI structure after AMP removal...")
    print("=" * 50)
    
    # Perbaiki struktur HTML
    content = fix_html_structure()
    
    # Buat CSS dan JS tambahan
    create_enhanced_css()
    create_enhanced_js()
    
    # Update HTML untuk include file tambahan
    update_html_with_fixes(content)
    
    print("\n✅ UI structure fixes complete!")
    print("\n📁 Files created:")
    print("   - index_ui_fixed.html (Fixed HTML structure)")
    print("   - css/ui-fixes.css (UI fixes CSS)")
    print("   - js/ui-fixes.js (UI fixes JavaScript)")
    
    print("\n🎯 Fixes applied:")
    print("   - ✅ Proper HTML5 structure")
    print("   - ✅ Removed all AMP remnants")
    print("   - ✅ Fixed duplicate elements")
    print("   - ✅ Fixed broken tags")
    print("   - ✅ Enhanced sidebar functionality")
    print("   - ✅ Fixed responsive design")
    
    print("\n🌐 Test URL:")
    print("   http://localhost:8080/index_ui_fixed.html")

def update_html_with_fixes(content):
    """Update HTML untuk include file fixes"""
    
    # Tambahkan CSS fixes
    css_link = '\n    <link rel="stylesheet" href="css/ui-fixes.css">'
    content = content.replace('</head>', css_link + '\n</head>')
    
    # Tambahkan JS fixes
    js_script = '\n    <script src="js/ui-fixes.js" defer></script>'
    content = content.replace('</body>', js_script + '\n</body>')
    
    # Simpan file final
    final_file = Path("index_ui_fixed.html")
    with open(final_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("🔧 Updated HTML with fixes")

if __name__ == "__main__":
    main()
