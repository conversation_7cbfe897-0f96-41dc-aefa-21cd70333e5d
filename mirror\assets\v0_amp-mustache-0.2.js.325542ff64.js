;
(self.AMP=self.AMP||[]).push({m:0,v:"2508201830000",n:"amp-mustache",ev:"0.2",l:!0,f:function(t,e){!function(){function e(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function n(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function r(t){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?n(Object(i),!0).forEach((function(n){e(t,n,i[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){return(o=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function s(t,e){if(e&&("object"===i(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function c(t,e){for(var n=[],r=0,i=0;i<t.length;i++){var o=t[i];e(o,i,t)?n.push(o):(r<i&&(t[r]=o),r++)}return r<t.length&&(t.length=r),n}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function l(t,e){if(t){if("string"==typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}Array.isArray;var p=Object.prototype;function m(t){var e=Object.create(null);return t&&Object.assign(e,t),e}function h(t){return 1==(null==t?void 0:t.nodeType)}function d(t){return h(t)?t.tagName.toLowerCase()+(t.id?"#".concat(t.id):""):t}function v(t,e,n,r,i,o,a,u,s,c,f){return t}function b(t,e){return t}function g(t,e,n,r,i,o,a,u,s,f,l){return function(t,e){var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Assertion failed";if(e)return e;t&&-1==i.indexOf(t)&&(i+=t);for(var o=3,a=i.split("%s"),u=a.shift(),s=[u];a.length;){var f=arguments[o++],l=a.shift();u+=d(f)+l,s.push(f,l.trim())}var p=new Error(u);throw p.messageArray=c(s,(function(t){return""!==t})),null===(n=(r=self).__AMP_REPORT_ERROR)||void 0===n||n.call(r,p),p}("​​​",t,e,n,r,i,o,a,u,s,f,l)}function y(t,e,n){return(y=u()?Reflect.construct:function(t,e,n){var r=[null];r.push.apply(r,e);var i=new(Function.bind.apply(t,r));return n&&o(i,n.prototype),i}).apply(null,arguments)}function w(t){return function(t){if(Array.isArray(t))return f(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||l(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}p.hasOwnProperty,p.toString;var x=Object.entries,k=Object.setPrototypeOf,E=Object.isFrozen,S=Object.getPrototypeOf,O=Object.getOwnPropertyDescriptor,A=Object.freeze,j=Object.seal,R=Object.create,T="undefined"!=typeof Reflect&&Reflect,_=T.apply,z=T.construct;_||(_=function(t,e,n){return t.apply(e,n)}),A||(A=function(t){return t}),j||(j=function(t){return t}),z||(z=function(t,e){return y(t,w(e))});var I,D=G(Array.prototype.forEach),L=G(Array.prototype.pop),F=G(Array.prototype.push),M=G(String.prototype.toLowerCase),C=G(String.prototype.toString),U=G(String.prototype.match),N=G(String.prototype.replace),P=G(String.prototype.indexOf),B=G(String.prototype.trim),W=G(RegExp.prototype.test),q=(I=TypeError,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return z(I,e)});function G(t){return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return _(t,e,r)}}function $(t,e,n){n=n||M,k&&k(t,null);for(var r=e.length;r--;){var i=e[r];if("string"==typeof i){var o=n(i);o!==i&&(E(e)||(e[r]=o),i=o)}t[i]=!0}return t}function H(t){for(var e,n=R(null),r=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=l(t))||t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(x(t));!(e=r()).done;){var i=(2,function(t){if(Array.isArray(t))return t}(u=e.value)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),2!==o.length);a=!0);}catch(t){u=!0,i=t}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(u)||l(u,2)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=i[0],a=i[1];n[o]=a}var u;return n}function Y(t,e){for(;null!==t;){var n=O(t,e);if(n){if(n.get)return G(n.get);if("function"==typeof n.value)return G(n.value)}t=S(t)}return function(t){return console.warn("fallback value for",t),null}}var X=A(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),V=A(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),K=A(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Z=A(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),J=A(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Q=A(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),tt=A(["#text"]),et=A(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),nt=A(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),rt=A(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),it=A(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ot=j(/\{\{[\w\W]*|[\w\W]*\}\}/gm),at=j(/<%[\w\W]*|[\w\W]*%>/gm),ut=j(/\${[\w\W]*}/gm),st=j(/^data-[\-\w.\u00B7-\uFFFF]/),ct=j(/^aria-[\-\w]+$/),ft=j(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),lt=j(/^(?:\w+script|data):/i),pt=j(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),mt=j(/^html$/i),ht=Object.freeze({__proto__:null,MUSTACHE_EXPR:ot,ERB_EXPR:at,TMPLIT_EXPR:ut,DATA_ATTR:st,ARIA_ATTR:ct,IS_ALLOWED_URI:ft,IS_SCRIPT_OR_DATA:lt,ATTR_WHITESPACE:pt,DOCTYPE_NAME:mt}),dt=function(){return"undefined"==typeof window?null:window},vt=function(t,e){if("object"!==i(t)||"function"!=typeof t.createPolicy)return null;var n=null,r="data-tt-policy-suffix";e.currentScript&&e.currentScript.hasAttribute(r)&&(n=e.currentScript.getAttribute(r));var o="dompurify"+(n?"#"+n:"");try{return t.createPolicy(o,{createHTML:function(t){return t},createScriptURL:function(t){return t}})}catch(t){return console.warn("TrustedTypes policy "+o+" could not be created."),null}},bt=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:dt(),n=function(e){return t(e)};if(n.version="3.0.2",n.removed=[],!e||!e.document||9!==e.document.nodeType)return n.isSupported=!1,n;var r=e.document,o=e.document,a=e.DocumentFragment,u=e.HTMLTemplateElement,s=e.Node,c=e.Element,f=e.NodeFilter,l=e.NamedNodeMap,p=void 0===l?e.NamedNodeMap||e.MozNamedAttrMap:l,m=e.HTMLFormElement,h=e.DOMParser,d=e.trustedTypes,v=c.prototype,b=Y(v,"cloneNode"),g=Y(v,"nextSibling"),y=Y(v,"childNodes"),k=Y(v,"parentNode");if("function"==typeof u){var E=o.createElement("template");E.content&&E.content.ownerDocument&&(o=E.content.ownerDocument)}var S=vt(d,r),O=S?S.createHTML(""):"",j=o,R=j.implementation,T=j.createNodeIterator,_=j.createDocumentFragment,z=j.getElementsByTagName,I=r.importNode,G={};n.isSupported="function"==typeof x&&"function"==typeof k&&R&&void 0!==R.createHTMLDocument;var ot,at,ut=ht.MUSTACHE_EXPR,st=ht.ERB_EXPR,ct=ht.TMPLIT_EXPR,lt=ht.DATA_ATTR,pt=ht.ARIA_ATTR,bt=ht.IS_SCRIPT_OR_DATA,gt=ht.ATTR_WHITESPACE,yt=ht.IS_ALLOWED_URI,wt=null,xt=$({},[].concat(w(X),w(V),w(K),w(J),w(tt))),kt=null,Et=$({},[].concat(w(et),w(nt),w(rt),w(it))),St=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ot=null,At=null,jt=!0,Rt=!0,Tt=!1,_t=!0,zt=!1,It=!1,Dt=!1,Lt=!1,Ft=!1,Mt=!1,Ct=!1,Ut=!0,Nt=!1,Pt="user-content-",Bt=!0,Wt=!1,qt={},Gt=null,$t=$({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Ht=null,Yt=$({},["audio","video","img","source","image","track"]),Xt=null,Vt=$({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Kt="http://www.w3.org/1998/Math/MathML",Zt="http://www.w3.org/2000/svg",Jt="http://www.w3.org/1999/xhtml",Qt=Jt,te=!1,ee=null,ne=$({},[Kt,Zt,Jt],C),re=["application/xhtml+xml","text/html"],ie="text/html",oe=null,ae=o.createElement("form"),ue=function(t){return t instanceof RegExp||t instanceof Function},se=function(t){oe&&oe===t||(t&&"object"===i(t)||(t={}),t=H(t),ot=ot=-1===re.indexOf(t.PARSER_MEDIA_TYPE)?ie:t.PARSER_MEDIA_TYPE,at="application/xhtml+xml"===ot?C:M,wt="ALLOWED_TAGS"in t?$({},t.ALLOWED_TAGS,at):xt,kt="ALLOWED_ATTR"in t?$({},t.ALLOWED_ATTR,at):Et,ee="ALLOWED_NAMESPACES"in t?$({},t.ALLOWED_NAMESPACES,C):ne,Xt="ADD_URI_SAFE_ATTR"in t?$(H(Vt),t.ADD_URI_SAFE_ATTR,at):Vt,Ht="ADD_DATA_URI_TAGS"in t?$(H(Yt),t.ADD_DATA_URI_TAGS,at):Yt,Gt="FORBID_CONTENTS"in t?$({},t.FORBID_CONTENTS,at):$t,Ot="FORBID_TAGS"in t?$({},t.FORBID_TAGS,at):{},At="FORBID_ATTR"in t?$({},t.FORBID_ATTR,at):{},qt="USE_PROFILES"in t&&t.USE_PROFILES,jt=!1!==t.ALLOW_ARIA_ATTR,Rt=!1!==t.ALLOW_DATA_ATTR,Tt=t.ALLOW_UNKNOWN_PROTOCOLS||!1,_t=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,zt=t.SAFE_FOR_TEMPLATES||!1,It=t.WHOLE_DOCUMENT||!1,Ft=t.RETURN_DOM||!1,Mt=t.RETURN_DOM_FRAGMENT||!1,Ct=t.RETURN_TRUSTED_TYPE||!1,Lt=t.FORCE_BODY||!1,Ut=!1!==t.SANITIZE_DOM,Nt=t.SANITIZE_NAMED_PROPS||!1,Bt=!1!==t.KEEP_CONTENT,Wt=t.IN_PLACE||!1,yt=t.ALLOWED_URI_REGEXP||ft,Qt=t.NAMESPACE||Jt,St=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&ue(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(St.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&ue(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(St.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(St.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),zt&&(Rt=!1),Mt&&(Ft=!0),qt&&(wt=$({},w(tt)),kt=[],!0===qt.html&&($(wt,X),$(kt,et)),!0===qt.svg&&($(wt,V),$(kt,nt),$(kt,it)),!0===qt.svgFilters&&($(wt,K),$(kt,nt),$(kt,it)),!0===qt.mathMl&&($(wt,J),$(kt,rt),$(kt,it))),t.ADD_TAGS&&(wt===xt&&(wt=H(wt)),$(wt,t.ADD_TAGS,at)),t.ADD_ATTR&&(kt===Et&&(kt=H(kt)),$(kt,t.ADD_ATTR,at)),t.ADD_URI_SAFE_ATTR&&$(Xt,t.ADD_URI_SAFE_ATTR,at),t.FORBID_CONTENTS&&(Gt===$t&&(Gt=H(Gt)),$(Gt,t.FORBID_CONTENTS,at)),Bt&&(wt["#text"]=!0),It&&$(wt,["html","head","body"]),wt.table&&($(wt,["tbody"]),delete Ot.tbody),A&&A(t),oe=t)},ce=$({},["mi","mo","mn","ms","mtext"]),fe=$({},["foreignobject","desc","title","annotation-xml"]),le=$({},["title","style","font","a","script"]),pe=$({},V);$(pe,K),$(pe,Z);var me=$({},J);$(me,Q);var he=function(t){var e=k(t);e&&e.tagName||(e={namespaceURI:Qt,tagName:"template"});var n=M(t.tagName),r=M(e.tagName);return!!ee[t.namespaceURI]&&(t.namespaceURI===Zt?e.namespaceURI===Jt?"svg"===n:e.namespaceURI===Kt?"svg"===n&&("annotation-xml"===r||ce[r]):Boolean(pe[n]):t.namespaceURI===Kt?e.namespaceURI===Jt?"math"===n:e.namespaceURI===Zt?"math"===n&&fe[r]:Boolean(me[n]):t.namespaceURI===Jt?!(e.namespaceURI===Zt&&!fe[r])&&!(e.namespaceURI===Kt&&!ce[r])&&!me[n]&&(le[n]||!pe[n]):!("application/xhtml+xml"!==ot||!ee[t.namespaceURI]))},de=function(t){F(n.removed,{element:t});try{t.parentNode.removeChild(t)}catch(e){t.remove()}},ve=function(t,e){try{F(n.removed,{attribute:e.getAttributeNode(t),from:e})}catch(t){F(n.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t&&!kt[t])if(Ft||Mt)try{de(e)}catch(t){}else try{e.setAttribute(t,"")}catch(t){}},be=function(t){var e,n;if(Lt)t="<remove></remove>"+t;else{var r=U(t,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===ot&&Qt===Jt&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");var i=S?S.createHTML(t):t;if(Qt===Jt)try{e=(new h).parseFromString(i,ot)}catch(t){}if(!e||!e.documentElement){e=R.createDocument(Qt,"template",null);try{e.documentElement.innerHTML=te?O:i}catch(t){}}var a=e.body||e.documentElement;return t&&n&&a.insertBefore(o.createTextNode(n),a.childNodes[0]||null),Qt===Jt?z.call(e,It?"html":"body")[0]:It?e.documentElement:a},ge=function(t){return T.call(t.ownerDocument||t,t,f.SHOW_ELEMENT|f.SHOW_COMMENT|f.SHOW_TEXT,null,!1)},ye=function(t){return t instanceof m&&("string"!=typeof t.nodeName||"string"!=typeof t.textContent||"function"!=typeof t.removeChild||!(t.attributes instanceof p)||"function"!=typeof t.removeAttribute||"function"!=typeof t.setAttribute||"string"!=typeof t.namespaceURI||"function"!=typeof t.insertBefore||"function"!=typeof t.hasChildNodes)},we=function(t){return"object"===i(s)?t instanceof s:t&&"object"===i(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},xe=function(t,e,r){G[t]&&D(G[t],(function(t){t.call(n,e,r,oe)}))},ke=function(t){var e;if(xe("beforeSanitizeElements",t,null),ye(t))return de(t),!0;var r=at(t.nodeName);if(xe("uponSanitizeElement",t,{tagName:r,allowedTags:wt}),t.hasChildNodes()&&!we(t.firstElementChild)&&(!we(t.content)||!we(t.content.firstElementChild))&&W(/<[/\w]/g,t.innerHTML)&&W(/<[/\w]/g,t.textContent))return de(t),!0;if(!wt[r]||Ot[r]){if(!Ot[r]&&Se(r)){if(St.tagNameCheck instanceof RegExp&&W(St.tagNameCheck,r))return!1;if(St.tagNameCheck instanceof Function&&St.tagNameCheck(r))return!1}if(Bt&&!Gt[r]){var i=k(t)||t.parentNode,o=y(t)||t.childNodes;if(o&&i)for(var a=o.length-1;a>=0;--a)i.insertBefore(b(o[a],!0),g(t))}return de(t),!0}return t instanceof c&&!he(t)?(de(t),!0):"noscript"!==r&&"noembed"!==r||!W(/<\/no(script|embed)/i,t.innerHTML)?(zt&&3===t.nodeType&&(e=t.textContent,e=N(e,ut," "),e=N(e,st," "),e=N(e,ct," "),t.textContent!==e&&(F(n.removed,{element:t.cloneNode()}),t.textContent=e)),xe("afterSanitizeElements",t,null),!1):(de(t),!0)},Ee=function(t,e,n){if(Ut&&("id"===e||"name"===e)&&(n in o||n in ae))return!1;if(Rt&&!At[e]&&W(lt,e));else if(jt&&W(pt,e));else if(!kt[e]||At[e]){if(!(Se(t)&&(St.tagNameCheck instanceof RegExp&&W(St.tagNameCheck,t)||St.tagNameCheck instanceof Function&&St.tagNameCheck(t))&&(St.attributeNameCheck instanceof RegExp&&W(St.attributeNameCheck,e)||St.attributeNameCheck instanceof Function&&St.attributeNameCheck(e))||"is"===e&&St.allowCustomizedBuiltInElements&&(St.tagNameCheck instanceof RegExp&&W(St.tagNameCheck,n)||St.tagNameCheck instanceof Function&&St.tagNameCheck(n))))return!1}else if(Xt[e]);else if(W(yt,N(n,gt,"")));else if("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==P(n,"data:")||!Ht[t])if(Tt&&!W(bt,N(n,gt,"")));else if(n)return!1;return!0},Se=function(t){return t.indexOf("-")>0},Oe=function(t){var e,r,o,a;xe("beforeSanitizeAttributes",t,null);var u=t.attributes;if(u){var s={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:kt};for(a=u.length;a--;){var c=e=u[a],f=c.name,l=c.namespaceURI;if(r="value"===f?e.value:B(e.value),o=at(f),s.attrName=o,s.attrValue=r,s.keepAttr=!0,s.forceKeepAttr=void 0,xe("uponSanitizeAttribute",t,s),r=s.attrValue,!s.forceKeepAttr&&(ve(f,t),s.keepAttr))if(_t||!W(/\/>/i,r)){zt&&(r=N(r,ut," "),r=N(r,st," "),r=N(r,ct," "));var p=at(t.nodeName);if(Ee(p,o,r)){if(!Nt||"id"!==o&&"name"!==o||(ve(f,t),r=Pt+r),S&&"object"===i(d)&&"function"==typeof d.getAttributeType)if(l);else switch(d.getAttributeType(p,o)){case"TrustedHTML":r=S.createHTML(r);break;case"TrustedScriptURL":r=S.createScriptURL(r)}try{l?t.setAttributeNS(l,f,r):t.setAttribute(f,r),L(n.removed)}catch(t){}}}else ve(f,t)}xe("afterSanitizeAttributes",t,null)}},Ae=function t(e){var n,r=ge(e);for(xe("beforeSanitizeShadowDOM",e,null);n=r.nextNode();)xe("uponSanitizeShadowNode",n,null),ke(n)||(n.content instanceof a&&t(n.content),Oe(n));xe("afterSanitizeShadowDOM",e,null)};return n.sanitize=function(t){var e,i,o,u,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((te=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!we(t)){if("function"!=typeof t.toString)throw q("toString is not a function");if("string"!=typeof(t=t.toString()))throw q("dirty is not a string, aborting")}if(!n.isSupported)return t;if(Dt||se(c),n.removed=[],"string"==typeof t&&(Wt=!1),Wt){if(t.nodeName){var f=at(t.nodeName);if(!wt[f]||Ot[f])throw q("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof s)1===(i=(e=be("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===i.nodeName||"HTML"===i.nodeName?e=i:e.appendChild(i);else{if(!Ft&&!zt&&!It&&-1===t.indexOf("<"))return S&&Ct?S.createHTML(t):t;if(!(e=be(t)))return Ft?null:Ct?O:""}e&&Lt&&de(e.firstChild);for(var l=ge(Wt?t:e);o=l.nextNode();)ke(o)||(o.content instanceof a&&Ae(o.content),Oe(o));if(Wt)return t;if(Ft){if(Mt)for(u=_.call(e.ownerDocument);e.firstChild;)u.appendChild(e.firstChild);else u=e;return(kt.shadowroot||kt.shadowrootmod)&&(u=I.call(r,u,!0)),u}var p=It?e.outerHTML:e.innerHTML;return It&&wt["!doctype"]&&e.ownerDocument&&e.ownerDocument.doctype&&e.ownerDocument.doctype.name&&W(mt,e.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+e.ownerDocument.doctype.name+">\n"+p),zt&&(p=N(p,ut," "),p=N(p,st," "),p=N(p,ct," ")),S&&Ct?S.createHTML(p):p},n.setConfig=function(t){se(t),Dt=!0},n.clearConfig=function(){oe=null,Dt=!1},n.isValidAttribute=function(t,e,n){oe||se({});var r=at(t),i=at(e);return Ee(r,i,n)},n.addHook=function(t,e){"function"==typeof e&&(G[t]=G[t]||[],F(G[t],e))},n.removeHook=function(t){if(G[t])return L(G[t])},n.removeHooks=function(t){G[t]&&(G[t]=[])},n.removeAllHooks=function(){G={}},n}();function gt(t){return function(t,e){var n=e.documentElement;return["⚡4email","amp4email"].some((function(t){return n.hasAttribute(t)}))}(0,t)}var yt=/(?:^[#?]?|&)([^=&]+)(?:=([^&]*))?/g;function wt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{return decodeURIComponent(t)}catch(t){return e}}self.__AMP_LOG=self.__AMP_LOG||{user:null,dev:null,userForEmbed:null};var xt=self.__AMP_LOG;function kt(t){return xt.user||(xt.user=Et()),function(t,e){return e&&e.ownerDocument.defaultView!=t}(xt.user.win,t)?xt.userForEmbed||(xt.userForEmbed=Et()):xt.user}function Et(t){return function(t,e){throw new Error("failed to call initLogConstructor")}()}function St(t,e,n,r,i,o,a,u,s,c,f){return t}function Ot(t,e,n,r,i,o,a,u,s,c,f){return kt().assert(t,e,n,r,i,o,a,u,s,c,f)}var At=/(\S+)(?:\s+(?:(-?\d+(?:\.\d+)?)([a-zA-Z]*)))?\s*(?:,|$)/g,jt=function(){function t(t){g(t.length>0,"Srcset must have at least one source"),this.JT=t;for(var e=!1,n=!1,r=0;r<t.length;r++){var i=t[r];e=e||!!i.width,n=n||!!i.dpr}g(!(e===n),"Srcset must have width or dpr sources, but not both"),t.sort(e?Rt:Tt),this.$R=e}var e=t.prototype;return e.select=function(t,e){var n;return v(t),v(e),n=this.$R?this.YR(t*e):this.QR(e),this.JT[n].url},e.YR=function(t){for(var e=this.JT,n=0,r=1/0,i=1/0,o=0;o<e.length;o++){var a,u=null!==(a=e[o].width)&&void 0!==a?a:0,s=Math.abs(u-t);if(!(s<=1.1*r||t/i>1.2))break;n=o,r=s,i=u}return n},e.QR=function(t){for(var e=this.JT,n=0,r=1/0,i=0;i<e.length;i++){var o=Math.abs(e[i].dpr-t);if(!(o<=r))break;n=i,r=o}return n},e.getUrls=function(){return this.JT.map((function(t){return t.url}))},e.stringify=function(t){for(var e=[],n=this.JT,r=0;r<n.length;r++){var i=n[r],o=i.url;t&&(o=t(o)),this.$R?o+=" ".concat(i.width,"w"):o+=" ".concat(i.dpr,"x"),e.push(o)}return e.join(", ")},t}();function Rt(t,e){return g(t.width!=e.width,"Duplicate width: %s",t.width),t.width-e.width}function Tt(t,e){return g(t.dpr!=e.dpr,"Duplicate dpr: %s",t.dpr),t.dpr-e.dpr}var _t=function(){function t(t){this.G=t,this.K=0,this.Y=0,this.rr=m()}var e=t.prototype;return e.has=function(t){return!!this.rr[t]},e.get=function(t){var e=this.rr[t];if(e)return e.access=++this.Y,e.payload},e.put=function(t,e){this.has(t)||this.K++,this.rr[t]={payload:e,access:this.Y},this.nr()},e.nr=function(){if(!(this.K<=this.G)){var t,e=this.rr,n=this.Y+1;for(var r in e){var i=e[r].access;i<n&&(n=i,t=r)}void 0!==t&&(delete e[t],this.K--)}},t}();function zt(t,e){return Lt(t=It(t),e)}function It(t){return t.__AMP_TOP||(t.__AMP_TOP=t)}function Dt(t){return t.nodeType?(n=t,e=(n.ownerDocument||n).defaultView,zt(e,"ampdoc")).getAmpDoc(t):t;var e,n}function Lt(t,e){St(function(t,e){var n=t.__AMP_SERVICES&&t.__AMP_SERVICES[e];return!(!n||!n.ctor)}(t,e));var n=Ft(t)[e];return n.obj||(St(n.ctor),St(n.context),n.obj=new n.ctor(n.context),St(n.obj),n.context=null,n.resolve&&n.resolve(n.obj)),n.obj}function Ft(t){var e=t.__AMP_SERVICES;return e||(e=t.__AMP_SERVICES={}),e}var Mt,Ct,Ut=function(){return self.AMP.config.urls}(),Nt=new Set(["c","v","a","ad"]),Pt=function(t){return"string"==typeof t?Bt(t):t};function Bt(t,e){return Mt||(Mt=self.document.createElement("a"),Ct=self.__AMP_URL_CACHE||(self.__AMP_URL_CACHE=new _t(100))),function(t,e,n){if(n&&n.has(e))return n.get(e);t.href=e,t.protocol||(t.href=t.href);var r,i={href:t.href,protocol:t.protocol,host:t.host,hostname:t.hostname,port:"0"==t.port?"":t.port,pathname:t.pathname,search:t.search,hash:t.hash,origin:null};"/"!==i.pathname[0]&&(i.pathname="/"+i.pathname),("http:"==i.protocol&&80==i.port||"https:"==i.protocol&&443==i.port)&&(i.port="",i.host=i.hostname),r=t.origin&&"null"!=t.origin?t.origin:"data:"!=i.protocol&&i.host?i.protocol+"//"+i.host:i.href,i.origin=r;var o=i;return n&&n.put(e,o),o}(Mt,t,e?null:Ct)}function Wt(t){return Ut.cdnProxyRegex.test(Pt(t).origin)}function qt(t,e){return e=Pt(e),"function"==typeof URL?new URL(t,e.href).toString():function(t,e){e=Pt(e);var n=Bt(t=t.replace(/\\/g,"/"));return t.toLowerCase().startsWith(n.protocol)?n.href:t.startsWith("//")?e.protocol+t:t.startsWith("/")?e.origin+t:e.origin+e.pathname.replace(/\/[^/]*$/,"/")+t}(t,e)}var Gt=function(){return self.AMP.config.urls}();function $t(t,e,n){return Ht(e)?function(t,e,n,r){var i;Ot(!("__amp_source_origin"in function(t){var e,n=m();if(!t)return n;for(;e=yt.exec(t);){var r=wt(e[1],e[1]),i=e[2]?wt(e[2].replace(/\+/g," "),e[2]):"";n[r]=i}return n}(Bt(i=n).search)),"Source origin is not allowed in %s",i);var o=Wt(r),a=Bt(function(t){if(!Wt(t=Pt(t)))return t.href;var e=t.pathname.split("/"),n=e[1];Ot(Nt.has(n),"Unknown path prefix in url %s",t.href);var r=e[2],i="s"==r?"https://"+decodeURIComponent(e[3]):"http://"+decodeURIComponent(r);return Ot(i.indexOf(".")>0,"Expected a . in origin %s",i),e.splice(1,"s"==r?3:2),i+e.join("/")+function(t,e){if(!t||"?"==t)return"";var n=new RegExp("[?&]".concat("(amp_(js[^&=]*|gsa|r|kit)|usqp)","\\b[^&]*"),"g"),r=t.replace(n,"").replace(/^[?&]/,"");return r?"?"+r:""}(t.search)+(t.hash||"")}(r));if("href"==e&&!n.startsWith("#"))return qt(n,a);if("src"==e)return"amp-img"==t?Yt(n,a,o):qt(n,a);if("srcset"==e){var u;try{u=function(t){for(var e,n=[];e=At.exec(t);){var r=e[1],i=void 0,o=void 0;if(e[2]){var a=e[3].toLowerCase();if("w"==a)i=parseInt(e[2],10);else{if("x"!=a)continue;o=parseFloat(e[2])}}else o=1;n.push({url:r,width:i,dpr:o})}return new jt(n)}(n)}catch(t){return kt().error("URL-REWRITE","Failed to parse srcset: ",t),n}return u.stringify((function(t){return Yt(t,a,o)}))}return n}(t,e,n,self.location):n}function Ht(t){return"src"==t||"href"==t||"xlink:href"==t||"srcset"==t}function Yt(t,e,n){var r=Bt(qt(t,e));return"data:"==r.protocol||Wt(r)||!n?r.href:"".concat(Gt.cdn,"/i/")+("https:"==r.protocol?"s/":"")+encodeURIComponent(r.host)+r.pathname+(r.search||"")+(r.hash||"")}var Xt="data-amp-bind-",Vt="i-amphtml-key",Kt={"AMP-IMG":["src","srcset","layout","width","height"]},Zt={"applet":!0,"audio":!0,"base":!0,"embed":!0,"frame":!0,"frameset":!0,"iframe":!0,"img":!0,"link":!0,"meta":!0,"object":!0,"style":!0,"video":!0},Jt={"amp-accordion":!0,"amp-anim":!0,"amp-bind-macro":!0,"amp-carousel":!0,"amp-fit-text":!0,"amp-img":!0,"amp-layout":!0,"amp-selector":!0,"amp-sidebar":!0,"amp-timeago":!0},Qt=["a","amp-img","article","aside","b","blockquote","br","caption","code","col","colgroup","dd","del","details","div","dl","dt","em","figcaption","figure","footer","h1","h2","h3","header","hr","i","ins","li","main","mark","nav","ol","p","pre","q","s","section","small","span","strong","sub","summary","sup","table","tbody","td","tfoot","th","thead","time","tr","u","ul"],te=["a","article","aside","b","blockquote","br","caption","code","col","colgroup","dd","del","details","div","dl","dt","em","figcaption","figure","footer","h1","h2","h3","header","hr","i","ins","li","main","mark","nav","ol","p","pre","q","s","section","small","span","strong","sub","summary","sup","table","tbody","td","tfoot","th","thead","time","tr","u","ul"],ee=["amp-fx","fallback","heights","layout","min-font-size","max-font-size","on","option","placeholder","submitting","submit-success","submit-error","validation-for","verify-error","visible-when-invalid","href","style","text","subscriptions-action","subscriptions-actions","subscriptions-decorate","subscriptions-dialog","subscriptions-display","subscriptions-section","subscriptions-service","subscriptions-google-rtc","amp-nested-submenu","amp-nested-submenu-open","amp-nested-submenu-close","itemprop"],ne={"a":["rel","target"],"div":["template"],"form":["action-xhr","verify-xhr","custom-validation-reporting","target"],"input":["mask-output"],"template":["type"],"textarea":["autoexpand"]},re=["_top","_blank"],ie=/^(?:\w+script|data|blob):/i,oe=/^(?:blob):/i,ae=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205f\u3000]/g,ue=Object.freeze({"input":{"type":/(?:image|button)/i}}),se=Object.freeze({"input":{"type":/(?:button|file|image|password)/i}}),ce=Object.freeze(["form","formaction","formmethod","formtarget","formnovalidate","formenctype"]),fe=Object.freeze({"input":ce,"textarea":ce,"select":ce}),le=Object.freeze({"amp-anim":["controls"],"form":["name"]}),pe=/!important|position\s*:\s*fixed|position\s*:\s*sticky/i;function me(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=n?n.replace(ae,""):"";if(!i){if(e.startsWith("on")&&"on"!=e)return!1;var a=o.toLowerCase();if(a.indexOf("<script")>=0||a.indexOf("</script")>=0)return!1;if(ie.test(o))return!1}if(oe.test(o))return!1;if("style"==e)return!pe.test(n);if("class"==e&&n&&/(^|\W)i-amphtml-/i.test(n))return!1;if(Ht(e)&&/__amp_source_origin/.test(n))return!1;var u=gt(r),s=Object.assign(m(),fe,u?le:{})[t];if(s&&-1!=s.indexOf(e))return!1;var c=Object.assign(m(),ue,u?se:{})[t];if(c){var f=c[e];if(f&&-1!=n.search(f))return!1}return!0}var he="purifier",de={"script":{"attribute":"type","values":["application/json","application/ld+json"]}},ve={USE_PROFILES:{html:!0,svg:!0,svgFilters:!0}},be=function(){function t(t,e,n){this.ql=t,this.qz=1,this.$z=bt(self),this.Xz=bt(self);var i=Object.assign(e||{},r(r({},ve),{},{ADD_ATTR:ee,ADD_TAGS:["use"],FORBID_TAGS:Object.keys(Zt),FORCE_BODY:!0,RETURN_DOM:!0,ALLOW_UNKNOWN_PROTOCOLS:!0}));this.$z.setConfig(i),this.Kz(this.$z,n),this.Zz(this.Xz)}var e=t.prototype;return e.purifyHtml=function(t){return this.$z.sanitize(t)},e.purifyTagsForTripleMustache=function(t){var e=this.Xz.sanitize(t,{"ALLOWED_TAGS":gt(this.ql)?te:Qt,"FORCE_BODY":!0,"RETURN_DOM_FRAGMENT":!0}),n=this.ql.createElement("div");return n.appendChild(e),n.innerHTML},e.getAllowedTags=function(){var t={};this.$z.addHook("uponSanitizeElement",(function(e,n){Object.assign(t,n.allowedTags)}));var e=this.ql.createElement("p");return this.$z.sanitize(e),Object.keys(Zt).forEach((function(e){t[e]=!1})),this.$z.removeHook("uponSanitizeElement"),t},e.validateAttributeChange=function(t,e,n){var r=t.nodeName.toLowerCase(),i=de[r];if(i){var o=i.attribute,a=i.values;if(o===e&&(null==n||!a.includes(n)))return!1}if("a"===r&&"target"===e&&(null==n||!re.includes(n)))return!1;if(null==n)return!0;if(ye(e)!==ge.NONE)return!1;if(!this.$z.isValidAttribute(r,e,n)){var u=ne[r];if(!(u&&u.includes(e)||r.startsWith("amp-")))return!1}var s=t.ownerDocument?t.ownerDocument:t;return!(n&&!me(r,e,n,s,!0))},e.Kz=function(t,e){var n,r,i=this,o=gt(this.ql),a=[],u=[];t.addHook("uponSanitizeElement",(function(t,e){var r=e.tagName;if(n=e.allowedTags,r.startsWith("amp-")&&(n[r]=!o||Jt[r]),"a"===r){var i=b(t);i.hasAttribute("href")&&!i.hasAttribute("target")&&i.setAttribute("target","_top")}var u=de[r];if(u){var s=u.attribute,c=u.values,f=b(t);f.hasAttribute(s)&&c.includes(f.getAttribute(s))&&(n[r]=!0,a.push(r))}})),t.addHook("afterSanitizeElements",(function(t){a.forEach((function(t){delete n[t]})),a.length=0})),t.addHook("uponSanitizeAttribute",(function(t,n){var o=t.nodeName.toLowerCase(),a=n.attrName,s=n.attrValue;r=n.allowedAttributes;var c=function(){r[a]||(r[a]=!0,u.push(a))};if(o.startsWith("amp-"))c();else{if("a"==o&&"target"==a){var f=s.toLowerCase();s=re.includes(f)?f:"_top"}var l=ne[o];l&&l.includes(a)&&c()}var p=ye(a);if(p===ge.CLASSIC){var m=a.substring(1,a.length-1);t.setAttribute("".concat(Xt).concat(m),s)}p!==ge.NONE&&t.setAttribute("i-amphtml-binding",""),me(o,a,s,i.ql,!0)?e&&s&&!a.startsWith(Xt)&&(s=e(o,a,s)):(n.keepAttr=!1,kt().error(he,'Removed invalid attribute %s[%s="%s"].',o,a,s)),n.attrValue=s})),t.addHook("afterSanitizeAttributes",(function(t){!function(t,e){var n=t.tagName.startsWith("AMP-"),r=t.hasAttribute("i-amphtml-binding");!r&&Kt[t.tagName]?t.setAttribute("i-amphtml-ignore",""):(r||n)&&(t.hasAttribute(Vt)||t.setAttribute(Vt,String(i.qz++)))}(t),u.forEach((function(t){delete r[t]})),u.length=0,"use"===t.nodeName.toLowerCase()&&["href","xlink:href"].forEach((function(e){t.hasAttribute(e)&&!t.getAttribute(e).startsWith("#")&&(function(t){var e;null===(e=t.parentElement)||void 0===e||e.removeChild(t)}(t),kt().error(he,'Removed invalid <use>. use[href] must start with "#".'))}))}))},e.Zz=function(t){var e;t.addHook("uponSanitizeElement",(function(t,n){var r=n.tagName;if(e=n.allowedTags,"template"===r){var i=t.getAttribute("type");i&&"amp-mustache"===i.toLowerCase()&&(e.template=!0)}})),t.addHook("afterSanitizeElements",(function(t){e.template=!1}))},t}(),ge={NONE:0,CLASSIC:1,ALTERNATIVE:2};function ye(t){return"["==t[0]&&"]"==t[t.length-1]?ge.CLASSIC:t.startsWith(Xt)?ge.ALTERNATIVE:ge.NONE}var we={};!function(t){var e=Object.prototype.toString,n=Array.isArray||function(t){return"[object Array]"===e.call(t)};function r(t){return"function"==typeof t}function o(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function a(t,e){return null!=t&&"object"===i(t)&&Object.prototype.hasOwnProperty.call(t,e)}var u=RegExp.prototype.test,s=/\S/,c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},f=/\s*/,l=/\s+/,p=/\s*=/,m=/\s*\}/,h=/#|\^|\/|>|\{|&|=|!/;function d(t){this.string=t,this.tail=t,this.pos=0}function v(t,e){this.view=t,this.cache={".":this.view},this.parent=e}function b(){this.cache={}}d.prototype.eos=function(){return""===this.tail},d.prototype.scan=function(t){var e=this.tail.match(t);if(!e||0!==e.index)return"";var n=e[0];return this.tail=this.tail.substring(n.length),this.pos+=n.length,n},d.prototype.scanUntil=function(t){var e,n=this.tail.search(t);switch(n){case-1:e=this.tail,this.tail="";break;case 0:e="";break;default:e=this.tail.substring(0,n),this.tail=this.tail.substring(n)}return this.pos+=e.length,e},v.prototype.push=function(t){return new v(t,this)},v.prototype.lookup=function(t){var e,n=this.cache;if(n.hasOwnProperty(t))e=n[t];else{for(var i,o,u=this,s=!1;u;){if(t.indexOf(".")>0)for(e=u.view,i=t.split("."),o=0;null!=e&&o<i.length;){if(!a(e,i[o])){e=null;break}o===i.length-1&&(s=!0),e=e[i[o++]]}else a(u.view,t)?(e=u.view[t],s=!0):e=null;if(s)break;u=u.parent}n[t]=e}return r(e)&&(e=e.call(this.view)),e},b.prototype.clearCache=function(){this.cache={}},b.prototype.parse=function(e,r){var i=this.cache,a=i[e];return null==a&&(a=i[e]=function(e,r){if(!e)return[];var i,a,c,v,b=[],g=[],y=[],w=!1,x=!1;function k(){if(w&&!x)for(;y.length;)delete g[y.pop()];else y=[];w=!1,x=!1}!function(t){if("string"==typeof t&&(t=t.split(l,2)),!n(t)||2!==t.length)throw new Error("Invalid tags: "+t);i=new RegExp(o(t[0])+"\\s*"),a=new RegExp("\\s*"+o(t[1])),c=new RegExp("\\s*"+o("}"+t[1]))}(r||t.tags);for(var E,S,O,A,j,R,T=new d(e);!T.eos();){if(E=T.pos,O=T.scanUntil(i))for(var _=0,z=O.length;_<z;++_)v=A=O.charAt(_),function(t,e){return u.call(t,e)}(s,v)?x=!0:y.push(g.length),g.push(["text",A,E,E+1]),E+=1,"\n"===A&&k();if(!T.scan(i))break;if(w=!0,S=T.scan(h)||"name",T.scan(f),"="===S?(O=T.scanUntil(p),T.scan(p),T.scanUntil(a)):"{"===S?(O=T.scanUntil(c),T.scan(m),T.scanUntil(a),S="&"):O=T.scanUntil(a),!T.scan(a))throw new Error("Unclosed tag at "+T.pos);if(j=[S,O,E,T.pos],g.push(j),"#"===S||"^"===S)b.push(j);else if("/"===S){if(!(R=b.pop()))throw new Error('Unopened section "'+O+'" at '+E);if(R[1]!==O)throw new Error('Unclosed section "'+R[1]+'" at '+E)}else"name"!==S&&"{"!==S&&"&"!==S||(x=!0)}if(R=b.pop())throw new Error('Unclosed section "'+R[1]+'" at '+T.pos);return function(t){for(var e,n=[],r=n,i=[],o=0,a=t.length;o<a;++o)switch((e=t[o])[0]){case"#":case"^":r.push(e),i.push(e),r=e[4]=[];break;case"/":i.pop()[5]=e[2],r=i.length>0?i[i.length-1][4]:n;break;default:r.push(e)}return n}(function(t){for(var e,n,r=[],i=0,o=t.length;i<o;++i)(e=t[i])&&("text"===e[0]&&n&&"text"===n[0]?(n[1]+=e[1],n[3]=e[3]):(r.push(e),n=e));return r}(g))}(e,r)),a},b.prototype.render=function(t,e,n){var r=this.parse(t),i=e instanceof v?e:new v(e);return this.renderTokens(r,i,n,t)},b.prototype.renderTokens=function(t,e,n,r){for(var i,o,a,u="",s=0,c=t.length;s<c;++s)a=void 0,"#"===(o=(i=t[s])[0])?a=this.renderSection(i,e,n,r):"^"===o?a=this.renderInverted(i,e,n,r):">"===o?a=this.renderPartial(i,e,n,r):"&"===o?a=this.unescapedValue(i,e):"name"===o?a=this.escapedValue(i,e):"text"===o&&(a=this.rawValue(i)),void 0!==a&&(u+=a);return u},b.prototype.renderSection=function(t,e,o,a){var u=this,s="",c=e.lookup(t[1]);if(c){if(n(c))for(var f=0,l=c.length;f<l;++f)s+=this.renderTokens(t[4],e.push(c[f]),o,a);else if("object"===i(c)||"string"==typeof c||"number"==typeof c)s+=this.renderTokens(t[4],e.push(c),o,a);else if(r(c)){if("string"!=typeof a)throw new Error("Cannot use higher-order sections without the original template");null!=(c=c.call(e.view,a.slice(t[3],t[5]),(function(t){return u.render(t,e,o)})))&&(s+=c)}else s+=this.renderTokens(t[4],e,o,a);return s}},b.prototype.renderInverted=function(t,e,r,i){var o=e.lookup(t[1]);if(!o||n(o)&&0===o.length)return this.renderTokens(t[4],e,r,i)},b.prototype.renderPartial=function(t,e,n){if(n){var i=r(n)?n(t[1]):n[t[1]];return null!=i?this.renderTokens(this.parse(i),e,n,i):void 0}},b.prototype.unescapedValue=function(e,n){var r=n.lookup(e[1]);if(null!=r)return t.sanitizeUnescaped?t.sanitizeUnescaped(r):r},b.prototype.escapedValue=function(e,n){var r=n.lookup(e[1]);if(null!=r)return t.escape(r)},b.prototype.rawValue=function(t){return t[1]},t.name="mustache.js",t.version="2.2.0",t.tags=["{{","}}"];var g=new b;t.clearCache=function(){return g.clearCache()},t.parse=function(t,e){return g.parse(t,e)},t.render=function(t,e,r){if("string"!=typeof t)throw new TypeError('Invalid template! Template should be a "string" but "'+(n(o=t)?"array":i(o))+'" was given as the first argument for mustache#render(template, view, partials)');var o;return g.render(t,e,r)},t.to_html=function(e,n,i,o){var a=t.render(e,n,i);if(!r(o))return a;o(a)},t.escape=function(t){return String(t).replace(/[&<>"'`=\/]/g,(function(t){return c[t]}))},t.sanitizeUnescaped=null,t.setUnescapedSanitizer=function(e){t.sanitizeUnescaped=e},t.Scanner=d,t.Context=v,t.Writer=b}(we);var xe=we,ke="amp-mustache",Ee=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&o(t,e)}(f,t);var e,n,c=(e=f,n=u(),function(){var t,r=a(e);if(n){var i=a(this).constructor;t=Reflect.construct(r,arguments,i)}else t=r.apply(this,arguments);return s(this,t)});function f(t,e){var n;return n=c.call(this,t,e),function(t,e,n,r){!function(t,e,n,r,i,o){var a=Ft(t),u=a[n];u||(u=a[n]={obj:null,promise:null,resolve:null,reject:null,context:null,ctor:null,sharedInstance:!1}),u.ctor||(u.ctor=r,u.context=e,u.sharedInstance=!1,u.resolve&&Lt(t,n))}(t=It(t),t,e,n)}(e,"purifier",(function(){return new be(e.document,{},$t)})),n.Jz=zt(e,"purifier"),xe.setUnescapedSanitizer((function(t){return n.Jz.purifyTagsForTripleMustache(t)})),n}var l=f.prototype;return l.compileCallback=function(){if(!this.viewerCanRenderTemplates()){this.Qz={},this.tD=this.eD();try{xe.parse(this.tD,void 0)}catch(t){kt().error(ke,t.message,this.element)}}},l.eD=function(){if("TEMPLATE"==this.element.tagName){var t=function(t){if("content"in t)return t.content.cloneNode(!0);var e=t.ownerDocument.createDocumentFragment();return function(t,e){for(var n=e.ownerDocument.createDocumentFragment(),r=t.firstChild;r;r=r.nextSibling)n.appendChild(r.cloneNode(!0));e.appendChild(n)}(t,e),e}(this.element);this.nD(t);var e=this.element.ownerDocument.createElement("div");return e.appendChild(t),e.innerHTML}return"SCRIPT"==this.element.tagName?this.element.textContent:""},l.nD=function(t){var e=this;t.querySelectorAll("template").forEach((function(t,n){var r="__AMP_NESTED_TEMPLATE_".concat(n);e.Qz[r]=t.outerHTML;var i=e.element.ownerDocument.createTextNode("{{{".concat(r,"}}}"));t.parentNode.replaceChild(i,t)}))},l.setHtml=function(t){var e="<div>".concat(t,"</div>"),n=this.tryUnwrap(this.rD(e));return this.unwrapChildren(n)},l.render=function(t){return this.tryUnwrap(this.Ch(t))},l.renderAsString=function(t){return this.Ch(t).innerHTML},l.Ch=function(t){var e=t;"object"===i(t)&&(e=r(r({},t),this.Qz));var n=xe.render(this.tD,e,void 0);return this.rD(n)},l.rD=function(t){return this.Jz.purifyHtml("<div>".concat(t,"</div>")).firstElementChild},f}(function(){function t(t,e){var n,r,i,o;this.element=t,this.win=t.ownerDocument.defaultView||e,this.Le=(n=this.element,r="viewer",Lt((i=Dt(n),(o=Dt(i)).isSingleDoc()?o.win:o),r)),this.compileCallback()}var e=t.prototype;return e.compileCallback=function(){},e.setHtml=function(t){},e.render=function(t){},e.renderAsString=function(t){},e.iD=function(t,e){for(var n=t.firstChild;null!=n;n=n.nextSibling)if(3==n.nodeType){var r=n.textContent.trim();r&&e(r)}else 8==n.nodeType||h(n)&&e(n)},e.tryUnwrap=function(t){var e;return this.iD(t,(function(t){e=void 0===e&&t.nodeType?t:null})),e||t},e.unwrapChildren=function(t){var e=this,n=[];return this.iD(t,(function(t){if("string"==typeof t){var r=e.win.document.createElement("div");r.textContent=t,n.push(r)}else n.push(t)})),n},e.viewerCanRenderTemplates=function(){return this.Le.hasCapability("viewerRenderTemplate")},t}());t.registerTemplate(ke,Ee)}();
/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */
/*!
* mustache.js - Logic-less {{mustache}} templates with JavaScript
* http://github.com/janl/mustache.js
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.2 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.2/LICENSE *)
*/}});
//# sourceMappingURL=amp-mustache-0.2.js.map