#!/usr/bin/env python3
"""
Script untuk mengekstrak semua data game dari HTML dan mengkonversinya ke JSON
"""

import re
import json
from pathlib import Path

def extract_games_from_html():
    """Ekstrak semua data game dari file HTML"""
    
    # Baca file HTML
    html_file = Path("index.html")
    if not html_file.exists():
        print("File index.html tidak ditemukan!")
        return []
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    games = []
    game_id = 1
    
    # Pattern untuk mencari card game
    card_pattern = r'<div class="card game-one-half-slot slots-games"[^>]*>(.*?)</div><div class="card game-one-half-slot slots-games"'
    
    # Cari semua card game
    cards = re.findall(card_pattern, content, re.DOTALL)
    
    for card_html in cards:
        try:
            game_data = extract_game_data(card_html, game_id)
            if game_data:
                games.append(game_data)
                game_id += 1
        except Exception as e:
            print(f"Error processing card {game_id}: {e}")
            continue
    
    return games

def extract_game_data(card_html, game_id):
    """Ekstrak data dari satu card game"""

    # Ekstrak nama game dari alt attribute (cari yang paling spesifik)
    name_matches = re.findall(r'alt="([^"]+)"', card_html)
    game_name = None
    for name in name_matches:
        if name and name != "{{ game_name }}" and len(name) > 3:
            game_name = name
            break

    if not game_name:
        game_name = f"Game {game_id}"

    # Ekstrak URL gambar lokal (yang sudah di-cache)
    img_match = re.search(r'src="(assets/[^"]+)"[^>]*class="i-amphtml-fill-content', card_html)
    image_url = img_match.group(1) if img_match else ""

    # Ekstrak URL gambar asli dari statics.hokibagus.club
    original_img_match = re.search(r'src="(https://statics\.hokibagus\.club/[^"]+)"', card_html)
    original_image_url = original_img_match.group(1) if original_img_match else ""

    # Ekstrak nilai RTP dan warna
    rtp_match = re.search(r'style="width: (\d+)%"[^>]*class="pbar-bg ([^"]+)"', card_html)
    if rtp_match:
        value = int(rtp_match.group(1))
        warna = rtp_match.group(2).strip()
    else:
        # Coba pattern alternatif
        rtp_alt_match = re.search(r'class="pbar-bg ([^"]+)"[^>]*style="width: (\d+)%"', card_html)
        if rtp_alt_match:
            warna = rtp_alt_match.group(1).strip()
            value = int(rtp_alt_match.group(2))
        else:
            value = 50
            warna = "yellow"

    # Ekstrak URL play
    play_url_match = re.search(r'href="([^"]+)"[^>]*class="hover-btn"', card_html)
    play_url = play_url_match.group(1) if play_url_match else "https://geng33041.com/"

    # Ekstrak pola RTP
    pola_rtp = extract_pola_patterns(card_html)

    # Tentukan provider dari URL gambar
    provider = "pragmatic"  # Default
    if original_image_url:
        if "pragmatic" in original_image_url.lower():
            provider = "pragmatic"
        elif "pgsoft" in original_image_url.lower():
            provider = "pgsoft"
        elif "habanero" in original_image_url.lower():
            provider = "habanero"
        elif "booming" in original_image_url.lower():
            provider = "booming"
        elif "microgaming" in original_image_url.lower():
            provider = "microgaming"

    return {
        "id": game_id,
        "game_name": game_name,
        "image_url": image_url,
        "original_image_url": original_image_url,
        "value": value,
        "warna": warna,
        "play_url": play_url,
        "provider": provider,
        "pola_rtp": pola_rtp
    }

def extract_pola_patterns(card_html):
    """Ekstrak pola RTP dari card HTML"""
    
    # Cek apakah ada pesan "Pola tidak tersedia"
    if "Pola tidak tersedia" in card_html:
        return {
            "available": False,
            "message": "Pola tidak tersedia!<br>Silahkan gunakan pola anda pribadi."
        }
    
    # Ekstrak semua span dengan id pola
    pola_pattern = r'<span id="([^"]+)">([^<]+)(?:<i[^>]*>([^<]+)</i>)*([^<]*)</span>'
    pola_matches = re.findall(pola_pattern, card_html)
    
    patterns = []
    for match in pola_matches:
        span_id = match[0]
        text_content = match[1].strip()
        
        # Parse bet amount dan type
        parts = text_content.split()
        if parts:
            bet = parts[0]
            type_text = " ".join(parts[1:]) if len(parts) > 1 else ""
            
            # Ekstrak icons dari HTML
            icons = extract_icons_from_span(card_html, span_id)
            
            patterns.append({
                "id": span_id,
                "bet": bet,
                "icons": icons,
                "type": type_text
            })
    
    return {
        "available": True,
        "patterns": patterns
    } if patterns else {
        "available": False,
        "message": "Pola tidak tersedia!<br>Silahkan gunakan pola anda pribadi."
    }

def extract_icons_from_span(card_html, span_id):
    """Ekstrak icons dari span tertentu"""
    
    # Cari span dengan id tertentu
    span_pattern = f'<span id="{re.escape(span_id)}"[^>]*>(.*?)</span>'
    span_match = re.search(span_pattern, card_html, re.DOTALL)
    
    if not span_match:
        return []
    
    span_content = span_match.group(1)
    
    # Ekstrak semua icon
    icon_pattern = r'<i[^>]*>([^<]+)</i>'
    icon_matches = re.findall(icon_pattern, span_content)
    
    return icon_matches

def main():
    """Main function"""
    print("Mengekstrak data game dari HTML...")
    
    games = extract_games_from_html()
    
    if not games:
        print("Tidak ada data game yang ditemukan!")
        return
    
    print(f"Berhasil mengekstrak {len(games)} game")
    
    # Simpan ke file JSON
    output_file = Path("games_extracted.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(games, f, indent=2, ensure_ascii=False)
    
    print(f"Data game disimpan ke {output_file}")
    
    # Tampilkan beberapa contoh
    print("\nContoh data game:")
    for i, game in enumerate(games[:3]):
        print(f"\nGame {i+1}:")
        print(f"  Nama: {game['game_name']}")
        print(f"  RTP: {game['value']}%")
        print(f"  Pola tersedia: {game['pola_rtp']['available']}")

if __name__ == "__main__":
    main()
