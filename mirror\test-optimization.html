<!DOCTYPE html>
<html amp lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Optimized Game Cards Test</title>
    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <script async custom-element="amp-list" src="https://cdn.ampproject.org/v0/amp-list-0.1.js"></script>
    <script async custom-template="amp-mustache" src="https://cdn.ampproject.org/v0/amp-mustache-0.2.js"></script>
    <style amp-boilerplate>body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}</style><noscript><style amp-boilerplate>body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}</style></noscript>
    
    <style amp-custom>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        /* Optimized loading styles */
        .optimized-list {
            contain: layout style paint;
            will-change: transform;
        }

        .img-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 150px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-spinner-large {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-placeholder {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error-fallback {
            text-align: center;
            padding: 20px;
            color: #d32f2f;
            background: #ffebee;
            border-radius: 8px;
            margin: 10px;
        }

        .error-fallback button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }

        /* Optimized card styles */
        .card {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
            transition: transform 0.2s ease-out;
            background: white;
            border-radius: 8px;
            margin: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card:hover {
            transform: translateY(-2px) scale(1.02);
        }

        .card-content {
            padding: 15px;
        }

        /* Lazy loading optimization */
        amp-img[loading="lazy"] {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        amp-img[loading="lazy"].i-amphtml-layout {
            opacity: 1;
        }

        /* Progress bar optimization */
        .pbar {
            position: relative;
            overflow: hidden;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            margin: 10px 0;
        }

        .pbar-bg {
            transition: width 0.5s ease-out;
            transform: translateZ(0);
            height: 100%;
            border-radius: 10px;
        }

        .pbar-bg.green { background: #4caf50; }
        .pbar-bg.yellow { background: #ff9800; }
        .pbar-bg.red { background: #f44336; }

        .pbar-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        /* Pola pattern styles */
        .pola-pattern {
            margin: 2px 0;
            font-size: 12px;
        }

        .icon-check {
            color: #00ff00 !important;
        }

        .icon-close {
            color: #ff0000 !important;
        }

        .pola-unavailable {
            color: #ffcc00;
            font-size: 11px;
        }

        .my-icon {
            background: #333 !important;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .play-btn {
            background: #4caf50;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .hover-btn {
            text-decoration: none;
        }

        .game-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <h1>Optimized Game Cards Test</h1>
    
    <div class="game-grid">
        <amp-list 
            width="auto" 
            height="600" 
            layout="fixed-height" 
            src="./games.json" 
            class="optimized-list"
            load-more="auto"
            max-items="10">
            <template type="amp-mustache">
                <div class="card" data-game-id="{{ id }}">
                    <div class="card-content">
                        <a class="hover-btn" href="{{ play_url }}" target="_top" rel="noopener">
                            <div class="play-btn">PLAY NOW</div>
                        </a>
                        <a href="{{ play_url }}" target="_top" rel="noopener">
                            <amp-img 
                                class="img-zoom" 
                                src="{{ image_url }}" 
                                alt="{{ game_name }}" 
                                layout="responsive" 
                                width="200" 
                                height="150"
                                loading="lazy">
                                <div placeholder class="img-placeholder">
                                    <div class="loading-spinner"></div>
                                </div>
                            </amp-img>
                        </a>
                        <h3>{{ game_name }}</h3>
                        <div class="pbar">
                            <div class="pbar-bg {{ warna }}" style="width: {{ value }}%">
                            </div>
                            <div class="pbar-text">{{ value }}%</div>
                        </div>

                        <div class="my-icon">
                            {{# pola_rtp.available }}
                                {{# pola_rtp.patterns }}
                                <div class="pola-pattern">
                                    {{ bet }} 
                                    {{# icons }}
                                    <i class="material-symbols-rounded icon-{{ . }}">{{ . }}</i>
                                    {{/ icons }}
                                    {{ type }}
                                </div>
                                {{/ pola_rtp.patterns }}
                            {{/ pola_rtp.available }}
                            {{^ pola_rtp.available }}
                                <div class="pola-unavailable">
                                    {{{ pola_rtp.message }}}
                                </div>
                            {{/ pola_rtp.available }}
                        </div>
                    </div>
                </div>
            </template>
            <div placeholder class="loading-placeholder">
                <div class="loading-spinner-large"></div>
                <p>Loading games...</p>
            </div>
            <div fallback class="error-fallback">
                <p>Failed to load games. Please try again.</p>
                <button>Retry</button>
            </div>
        </amp-list>
    </div>
</body>
</html>
