.game-card:hover {transform:translateY(-4px) scale(1.02);box-shadow:0 8px 25px rgba(0,0,0,0.15)} .game-card:hover .game-image img {transform:scale(1.1)} .play-btn {position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:var(--success-color);color:white;padding:0.5rem 1rem;border-radius:var(--border-radius);font-weight:600;text-decoration:none;opacity:0;transition:var(--transition);z-index:10} .game-card:hover .play-btn {opacity:1} .rtp-container {margin:0.75rem 0} .rtp-bar {height:8px;background:#e0e0e0;border-radius:4px;overflow:hidden;position:relative} .rtp-fill {height:100%;transition:width 0.8s ease-out;border-radius:4px} .rtp-fill.green {background:var(--success-color)} .rtp-fill.yellow {background:var(--warning-color)} .rtp-fill.red {background:var(--danger-color)} .rtp-text {font-size:0.9rem;font-weight:600;margin-top:0.25rem;text-align:center} .pola-section {background:var(--dark-color);color:white;padding:0.75rem;border-radius:var(--border-radius);margin-top:0.75rem;font-size:0.85rem} .pola-pattern {margin:0.25rem 0;display:flex;align-items:center;gap:0.5rem} .pola-icons {display:flex;gap:0.25rem} .icon-check {color:#4caf50} .icon-close {color:#f44336} .pola-unavailable {color:#ffcc00;text-align:center} .fade-in {animation:fadeIn 0.5s ease-in} @keyframes fadeIn {from {opacity:0;transform:translateY(20px)} to {opacity:1;transform:translateY(0)} } .skeleton {background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);background-size:200% 100%;animation:loading 1.5s infinite} @keyframes loading {0% {background-position:200% 0} 100% {background-position:-200% 0} } .skeleton-image {width:100%;height:160px;border-radius:var(--border-radius) var(--border-radius) 0 0} .skeleton-title {height:1.2rem;margin:1rem;border-radius:4px} .skeleton-rtp {height:8px;margin:1rem;border-radius:4px} .lazy-image {opacity:0;transition:opacity 0.3s ease-in-out} .lazy-image.loaded {opacity:1} .image-placeholder {background:#f5f5f5;display:flex;align-items:center;justify-content:center;color:#999;font-size:2rem} .no-results {text-align:center;padding:3rem;color:white} .no-results-icon {font-size:4rem;margin-bottom:1rem;opacity:0.7} .footer {text-align:center;padding:2rem;color:rgba(255,255,255,0.8);margin-top:3rem} @media (max-width:768px) {.search-input {font-size:16px} .game-card:hover {transform:none} .play-btn {opacity:1;position:static;transform:none;margin-bottom:0.5rem;display:inline-block} } @media (-webkit-min-device-pixel-ratio:2),(min-resolution:192dpi) {.game-image img {image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges} } @media (prefers-reduced-motion:reduce) {* {animation-duration:0.01ms !important;animation-iteration-count:1 !important;transition-duration:0.01ms !important} } @media (prefers-color-scheme:dark) {:root {--dark-color:#f0f0f0;--light-color:#333} .game-card {background:#2a2a2a;color:#f0f0f0} .search-input {background:rgba(42,42,42,0.9);color:#f0f0f0;border-color:rgba(255,255,255,0.2)} } @media print {.header,.search-container,.footer {display:none} .game-card {break-inside:avoid;box-shadow:none;border:1px solid #ccc} .games-grid {grid-template-columns:repeat(2,1fr)} }