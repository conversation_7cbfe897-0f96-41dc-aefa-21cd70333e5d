document.addEventListener('DOMContentLoaded',function() {const searchInput = document.querySelector('input[type="search"],.search');if (searchInput) {searchInput.addEventListener('input',function(e) {const searchTerm = e.target.value.toLowerCase();filterGames(searchTerm);});}const sidebarToggle = document.querySelector('[on*="sidebar"]');const sidebar = document.querySelector('amp-sidebar,.sidebar');if (sidebarToggle && sidebar) {sidebarToggle.addEventListener('click',function() {sidebar.classList.toggle('open');});}const forms = document.querySelectorAll('form');forms.forEach(form => {form.addEventListener('submit',function(e) {e.preventDefault();console.log('Form submitted');});});});function filterGames(searchTerm) {const gameCards = document.querySelectorAll('.card');gameCards.forEach(card => {const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';if (gameName.includes(searchTerm)) {card.style.display = 'block';}else {card.style.display = 'none';}});}function initLazyLoading() {const images = document.querySelectorAll('img[data-src]');const imageObserver = new IntersectionObserver((entries,observer) => {entries.forEach(entry => {if (entry.isIntersecting) {const img = entry.target;img.src = img.dataset.src;img.removeAttribute('data-src');observer.unobserve(img);}});});images.forEach(img => imageObserver.observe(img));}if (document.readyState === 'loading') {document.addEventListener('DOMContentLoaded',initLazyLoading);}else {initLazyLoading();}