
// Ultra Lightning Fast JavaScript
class UltraLightningApp {
    constructor() {
        this.games = [];
        this.isLoading = false;
        this.searchTimeout = null;
        
        // Performance monitoring
        this.metrics = {
            startTime: performance.now(),
            loadTime: 0,
            searchTime: 0
        };
        
        this.init();
    }
    
    init() {
        // Use requestIdleCallback for non-critical initialization
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => this.setup(), { timeout: 1000 });
        } else {
            setTimeout(() => this.setup(), 0);
        }
    }
    
    async setup() {
        console.log('🚀 Ultra Lightning App starting...');
        
        // Critical path first
        this.initializeSearch();
        this.initializeSidebar();
        
        // Non-critical features
        requestIdleCallback(() => {
            this.initializeLazyLoading();
            this.loadGames();
            this.initializeServiceWorker();
            this.initializePerformanceMonitoring();
        });
    }
    
    initializeSearch() {
        const searchInput = document.querySelector('input[type="search"]');
        if (!searchInput) return;
        
        // Use passive listeners and optimized debounce
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                const startTime = performance.now();
                this.filterGames(e.target.value.toLowerCase());
                this.metrics.searchTime = performance.now() - startTime;
            }, 100);
        }, { passive: true });
        
        console.log('🔍 Ultra search initialized');
    }
    
    filterGames(searchTerm) {
        const gameCards = document.querySelectorAll('.card');
        
        // Use DocumentFragment for batch DOM updates
        const fragment = document.createDocumentFragment();
        let visibleCount = 0;
        
        // Batch DOM reads and writes
        const updates = [];
        
        gameCards.forEach(card => {
            const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';
            const gameText = card.textContent?.toLowerCase() || '';
            
            const isVisible = !searchTerm || 
                             gameName.includes(searchTerm) || 
                             gameText.includes(searchTerm);
            
            updates.push({ card, isVisible });
            if (isVisible) visibleCount++;
        });
        
        // Apply all updates in one batch
        requestAnimationFrame(() => {
            updates.forEach(({ card, isVisible }) => {
                card.style.display = isVisible ? '' : 'none';
            });
        });
        
        console.log(`🎮 ${visibleCount} games visible in ${this.metrics.searchTime.toFixed(2)}ms`);
    }
    
    initializeSidebar() {
        const toggle = document.querySelector('.side');
        const sidebar = document.querySelector('#sidebar1');
        
        if (!toggle || !sidebar) return;
        
        // Use CSS custom properties for smooth animations
        sidebar.style.setProperty('--sidebar-transform', 'translateX(100%)');
        
        toggle.addEventListener('click', (e) => {
            e.preventDefault();
            
            const isOpen = sidebar.classList.toggle('open');
            sidebar.style.setProperty(
                '--sidebar-transform', 
                isOpen ? 'translateX(0)' : 'translateX(100%)'
            );
        }, { passive: false });
        
        console.log('📱 Ultra sidebar initialized');
    }
    
    initializeLazyLoading() {
        if (!('IntersectionObserver' in window)) return;
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    
                    // Preload image
                    if (img.dataset.src) {
                        const preloadImg = new Image();
                        preloadImg.onload = () => {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            img.classList.add('loaded');
                        };
                        preloadImg.src = img.dataset.src;
                    } else {
                        img.classList.add('loaded');
                    }
                    
                    imageObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: '100px',
            threshold: 0.1
        });
        
        // Observe all images
        document.querySelectorAll('img[data-src], img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
        
        console.log('🖼️ Ultra lazy loading initialized');
    }
    
    async loadGames() {
        if (this.isLoading) return;
        this.isLoading = true;
        
        try {
            // Use fetch with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);
            
            const response = await fetch('/games.json', {
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (response.ok) {
                const data = await response.json();
                this.games = data.items || [];
                
                console.log(`🎮 Ultra loaded ${this.games.length} games`);
                this.enhanceGameCards();
            }
        } catch (error) {
            console.error('❌ Failed to load games:', error);
        } finally {
            this.isLoading = false;
        }
    }
    
    enhanceGameCards() {
        const cards = document.querySelectorAll('.card');
        
        // Use event delegation for better performance
        document.addEventListener('mouseover', (e) => {
            const card = e.target.closest('.card');
            if (card) {
                card.style.transform = 'translateY(-2px) scale(1.02)';
                card.style.transition = 'transform 0.15s ease-out';
            }
        }, { passive: true });
        
        document.addEventListener('mouseout', (e) => {
            const card = e.target.closest('.card');
            if (card) {
                card.style.transform = '';
            }
        }, { passive: true });
        
        console.log(`✨ Ultra enhanced ${cards.length} game cards`);
    }
    
    async initializeServiceWorker() {
        if (!('serviceWorker' in navigator)) return;
        
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('🔧 Ultra Service Worker registered');
            
            // Listen for updates
            registration.addEventListener('updatefound', () => {
                console.log('🔄 Service Worker update available');
            });
        } catch (error) {
            console.log('❌ Service Worker registration failed:', error);
        }
    }
    
    initializePerformanceMonitoring() {
        // Monitor Web Vitals
        this.metrics.loadTime = performance.now() - this.metrics.startTime;
        
        // Report performance metrics
        console.log('📊 Performance Metrics:', {
            loadTime: `${this.metrics.loadTime.toFixed(2)}ms`,
            searchTime: `${this.metrics.searchTime.toFixed(2)}ms`,
            memoryUsage: this.getMemoryUsage()
        });
        
        // Monitor FPS
        this.monitorFPS();
    }
    
    getMemoryUsage() {
        if ('memory' in performance) {
            const memory = performance.memory;
            return {
                used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)}MB`,
                total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)}MB`
            };
        }
        return 'Not available';
    }
    
    monitorFPS() {
        let frames = 0;
        let lastTime = performance.now();
        
        const countFPS = () => {
            frames++;
            const currentTime = performance.now();
            
            if (currentTime >= lastTime + 1000) {
                console.log(`🎯 FPS: ${frames}`);
                frames = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(countFPS);
        };
        
        requestAnimationFrame(countFPS);
    }
}

// Initialize Ultra Lightning App
const ultraApp = new UltraLightningApp();

// Performance observer
if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
            if (entry.entryType === 'navigation') {
                console.log(`⚡ Navigation timing: ${entry.loadEventEnd - entry.loadEventStart}ms`);
            }
        });
    });
    
    observer.observe({ entryTypes: ['navigation'] });
}

// Export for global access
window.UltraLightningApp = ultraApp;
