<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>RTP Gengtoto Paling Gacor</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="games.json" as="fetch" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/icon?family=Material+Symbols+Rounded" as="style">
    
    <!-- Meta tags -->
    <meta name="description" content="RTP Gengtoto Paling Gacor">
    <meta name="googlebot" content="noindex">
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" type="image/png" href="assets/favicon.png.03f868b8e3.png">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Symbols+Rounded" rel="stylesheet">
    
    <style>
        /* Critical CSS - Above the fold */
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --dark-color: #333;
            --light-color: #f8f9fa;
            --border-radius: 8px;
            --box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--dark-color);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--box-shadow);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .logo {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        /* Search */
        .search-container {
            margin: 2rem 0;
            text-align: center;
        }

        .search-input {
            width: 100%;
            max-width: 400px;
            padding: 0.75rem 1rem;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: var(--border-radius);
            background: rgba(255,255,255,0.9);
            font-size: 1rem;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 3rem;
            color: white;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Games Grid */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            padding: 2rem 0;
        }

        /* Game Card */
        .game-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            transition: var(--transition);
            transform: translateZ(0);
        }

        .game-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .game-image {
            position: relative;
            width: 100%;
            height: 160px;
            overflow: hidden;
        }

        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .game-card:hover .game-image img {
            transform: scale(1.1);
        }

        .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--success-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            text-decoration: none;
            opacity: 0;
            transition: var(--transition);
        }

        .game-card:hover .play-btn {
            opacity: 1;
        }

        .game-content {
            padding: 1rem;
        }

        .game-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        /* RTP Bar */
        .rtp-container {
            margin: 0.75rem 0;
        }

        .rtp-bar {
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .rtp-fill {
            height: 100%;
            transition: width 0.8s ease-out;
            border-radius: 4px;
        }

        .rtp-fill.green { background: var(--success-color); }
        .rtp-fill.yellow { background: var(--warning-color); }
        .rtp-fill.red { background: var(--danger-color); }

        .rtp-text {
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 0.25rem;
            text-align: center;
        }

        /* Pola Section */
        .pola-section {
            background: var(--dark-color);
            color: white;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            margin-top: 0.75rem;
            font-size: 0.85rem;
        }

        .pola-pattern {
            margin: 0.25rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .pola-icons {
            display: flex;
            gap: 0.25rem;
        }

        .icon-check { color: #4caf50; }
        .icon-close { color: #f44336; }

        .pola-unavailable {
            color: #ffcc00;
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .games-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1rem;
                padding: 1rem 0;
            }
            
            .container {
                padding: 0 0.5rem;
            }
        }

        /* Utilities */
        .hidden { display: none; }
        .fade-in { animation: fadeIn 0.5s ease-in; }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: rgba(255,255,255,0.8);
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">🎰 RTP Gengtoto Paling Gacor</div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container">
        <!-- Search -->
        <div class="search-container">
            <input 
                type="text" 
                class="search-input" 
                placeholder="🔍 Cari Game..." 
                id="searchInput"
                autocomplete="off">
        </div>

        <!-- Loading -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Loading games...</p>
        </div>

        <!-- Games Grid -->
        <div id="gamesGrid" class="games-grid hidden"></div>

        <!-- No Results -->
        <div id="noResults" class="loading hidden">
            <p>🚫 Tidak ada game yang ditemukan</p>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            Copyright © 2018 gengtoto All rights reserved.
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        class GameApp {
            constructor() {
                this.games = [];
                this.filteredGames = [];
                this.searchInput = document.getElementById('searchInput');
                this.gamesGrid = document.getElementById('gamesGrid');
                this.loading = document.getElementById('loading');
                this.noResults = document.getElementById('noResults');
                
                this.init();
            }

            async init() {
                await this.loadGames();
                this.setupEventListeners();
                this.renderGames();
            }

            async loadGames() {
                try {
                    const response = await fetch('games.json');
                    if (!response.ok) throw new Error('Failed to load games');
                    
                    this.games = await response.json();
                    this.filteredGames = [...this.games];
                    
                    console.log(`Loaded ${this.games.length} games`);
                } catch (error) {
                    console.error('Error loading games:', error);
                    this.showError('Failed to load games. Please refresh the page.');
                }
            }

            setupEventListeners() {
                // Search with debounce
                let searchTimeout;
                this.searchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        this.filterGames(e.target.value);
                    }, 300);
                });

                // Intersection Observer for lazy loading images
                this.setupLazyLoading();
            }

            filterGames(searchTerm) {
                const term = searchTerm.toLowerCase().trim();
                
                if (!term) {
                    this.filteredGames = [...this.games];
                } else {
                    this.filteredGames = this.games.filter(game => 
                        game.game_name.toLowerCase().includes(term) ||
                        game.provider.toLowerCase().includes(term)
                    );
                }
                
                this.renderGames();
            }

            renderGames() {
                this.loading.classList.add('hidden');
                
                if (this.filteredGames.length === 0) {
                    this.gamesGrid.classList.add('hidden');
                    this.noResults.classList.remove('hidden');
                    return;
                }

                this.noResults.classList.add('hidden');
                this.gamesGrid.classList.remove('hidden');
                
                this.gamesGrid.innerHTML = this.filteredGames.map(game => this.createGameCard(game)).join('');
                
                // Trigger fade-in animation
                requestAnimationFrame(() => {
                    this.gamesGrid.querySelectorAll('.game-card').forEach((card, index) => {
                        setTimeout(() => {
                            card.classList.add('fade-in');
                        }, index * 50);
                    });
                });
            }

            createGameCard(game) {
                const polaContent = this.createPolaContent(game.pola_rtp);
                
                return `
                    <div class="game-card" data-game-id="${game.id}">
                        <div class="game-image">
                            <img 
                                data-src="${game.image_url}" 
                                alt="${game.game_name}"
                                class="lazy-image"
                                loading="lazy">
                            <a href="${game.play_url}" class="play-btn" target="_blank" rel="noopener">
                                ▶ PLAY NOW
                            </a>
                        </div>
                        <div class="game-content">
                            <h3 class="game-title">${game.game_name}</h3>
                            
                            <div class="rtp-container">
                                <div class="rtp-bar">
                                    <div class="rtp-fill ${game.warna}" style="width: ${game.value}%"></div>
                                </div>
                                <div class="rtp-text">${game.value}% RTP</div>
                            </div>
                            
                            <div class="pola-section">
                                ${polaContent}
                            </div>
                        </div>
                    </div>
                `;
            }

            createPolaContent(polaRtp) {
                if (!polaRtp.available) {
                    return `<div class="pola-unavailable">${polaRtp.message}</div>`;
                }

                return polaRtp.patterns.map(pattern => {
                    const icons = pattern.icons.map(icon => 
                        `<span class="material-symbols-rounded icon-${icon}">${icon}</span>`
                    ).join('');
                    
                    return `
                        <div class="pola-pattern">
                            <span>${pattern.bet}</span>
                            <div class="pola-icons">${icons}</div>
                            <span>${pattern.type}</span>
                        </div>
                    `;
                }).join('');
            }

            setupLazyLoading() {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy-image');
                            observer.unobserve(img);
                        }
                    });
                });

                // Observe images as they're added
                const observeImages = () => {
                    document.querySelectorAll('.lazy-image').forEach(img => {
                        imageObserver.observe(img);
                    });
                };

                // Initial observation and re-observe after renders
                const originalRenderGames = this.renderGames.bind(this);
                this.renderGames = function() {
                    originalRenderGames();
                    setTimeout(observeImages, 100);
                };
            }

            showError(message) {
                this.loading.innerHTML = `<p style="color: #f44336;">❌ ${message}</p>`;
            }
        }

        // Initialize app when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => new GameApp());
        } else {
            new GameApp();
        }

        // Performance optimizations

        // Preload critical resources
        const preloadCriticalResources = () => {
            // Preload first few game images
            fetch('games.json')
                .then(response => response.json())
                .then(games => {
                    games.slice(0, 6).forEach(game => {
                        const link = document.createElement('link');
                        link.rel = 'preload';
                        link.as = 'image';
                        link.href = game.image_url;
                        document.head.appendChild(link);
                    });
                });
        };

        // Service Worker for caching
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered');
                        preloadCriticalResources();
                    })
                    .catch(error => console.log('SW registration failed'));
            });
        } else {
            preloadCriticalResources();
        }
    </script>
</body>
</html>
