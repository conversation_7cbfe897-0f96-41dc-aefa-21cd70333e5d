
// UI Fixes JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 UI Fixes loaded');
    
    // Initialize sidebar
    initializeSidebar();
    
    // Initialize submenu
    initializeSubmenu();
    
    // Fix broken elements
    fixBrokenElements();
});

function initializeSidebar() {
    const sidebarToggle = document.querySelector('.side');
    const sidebar = document.querySelector('.side-bar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleSidebar();
        });
        
        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                closeSidebar();
            }
        });
        
        console.log('📱 Sidebar initialized');
    }
}

function toggleSidebar() {
    const sidebar = document.querySelector('.side-bar');
    if (sidebar) {
        sidebar.classList.toggle('open');
    }
}

function closeSidebar() {
    const sidebar = document.querySelector('.side-bar');
    if (sidebar) {
        sidebar.classList.remove('open');
    }
}

function initializeSubmenu() {
    const submenuToggles = document.querySelectorAll('.submenu-toggle');
    
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            this.classList.toggle('active');
            const submenu = this.nextElementSibling;
            if (submenu && submenu.classList.contains('submenu')) {
                submenu.style.display = submenu.style.display === 'block' ? 'none' : 'block';
            }
        });
    });
    
    // Handle back buttons
    const backButtons = document.querySelectorAll('.submenu-back');
    backButtons.forEach(back => {
        back.addEventListener('click', function() {
            const submenu = this.closest('.submenu');
            const toggle = submenu ? submenu.previousElementSibling : null;
            
            if (submenu) submenu.style.display = 'none';
            if (toggle) toggle.classList.remove('active');
        });
    });
    
    console.log('📋 Submenu initialized');
}

function fixBrokenElements() {
    // Fix images without proper attributes
    const images = document.querySelectorAll('img:not([alt])');
    images.forEach(img => {
        if (!img.alt) {
            img.alt = 'Image';
        }
    });
    
    // Fix links without proper attributes
    const links = document.querySelectorAll('a[href=""]');
    links.forEach(link => {
        link.href = '#';
        link.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });
    
    console.log('🔧 Fixed broken elements');
}

// Global functions
window.toggleSidebar = toggleSidebar;
window.closeSidebar = closeSidebar;


// Additional JavaScript fixes
document.addEventListener('DOMContentLoaded', function() {
    // Fix any remaining issues
    fixRemainingIssues();
    
    // Initialize enhanced features
    initializeEnhancedFeatures();
});

function fixRemainingIssues() {
    // Fix buttons without proper classes
    const buttons = document.querySelectorAll('button:not(.btn)');
    buttons.forEach(button => {
        if (!button.classList.contains('btn')) {
            button.classList.add('btn');
        }
    });
    
    // Fix images without alt text
    const images = document.querySelectorAll('img:not([alt])');
    images.forEach(img => {
        img.alt = 'Image';
    });
    
    // Fix links without href
    const links = document.querySelectorAll('a:not([href])');
    links.forEach(link => {
        link.href = '#';
        link.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });
    
    console.log('🔧 Fixed remaining issues');
}

function initializeEnhancedFeatures() {
    // Enhanced search functionality
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterContent(searchTerm);
        }, 300));
    }
    
    // Enhanced card interactions
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
    
    console.log('✨ Enhanced features initialized');
}

function filterContent(searchTerm) {
    const cards = document.querySelectorAll('.card');
    let visibleCount = 0;
    
    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        const isVisible = !searchTerm || text.includes(searchTerm);
        
        card.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });
    
    console.log(`🔍 ${visibleCount} items visible`);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Enhanced sidebar functionality
function toggleSidebar() {
    const sidebar = document.querySelector('.side-bar, #sidebar1');
    if (sidebar) {
        sidebar.classList.toggle('open');
        console.log('📱 Sidebar toggled');
    }
}

function closeSidebar() {
    const sidebar = document.querySelector('.side-bar, #sidebar1');
    if (sidebar) {
        sidebar.classList.remove('open');
        console.log('📱 Sidebar closed');
    }
}

// Make functions globally available
window.toggleSidebar = toggleSidebar;
window.closeSidebar = closeSidebar;
