#!/usr/bin/env python3
"""
Script untuk memperbaiki elemen yang hilang setelah verifikasi
"""

import re
from pathlib import Path

def fix_missing_elements():
    """Perbaiki elemen yang hilang"""
    
    # Baca file yang perlu diperbaiki
    ui_fixed_file = Path("index_ui_fixed.html")
    with open(ui_fixed_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 Fixing missing elements...")
    
    # Perbaiki elemen yang hilang
    content = fix_sidebar_container(content)
    content = fix_menu_button(content)
    content = fix_sidebar_toggle(content)
    content = remove_remaining_amp_classes(content)
    content = fix_button_elements(content)
    
    # Simpan file yang sudah diperbaiki
    output_file = Path("index_final.html")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Missing elements fixed: {output_file}")
    return content

def fix_sidebar_container(content):
    """Perbaiki sidebar container yang hilang"""
    
    # Cari sidebar yang ada dan perbaiki class
    sidebar_pattern = r'<div[^>]*class="[^"]*side-bar[^"]*"[^>]*id="sidebar1"[^>]*>'
    
    if not re.search(sidebar_pattern, content):
        # Cari div dengan id="sidebar1" dan tambahkan class yang benar
        content = re.sub(
            r'<div([^>]*)id="sidebar1"([^>]*)>',
            r'<div\1id="sidebar1"\2 class="side-bar">',
            content
        )
        print("🔧 Fixed sidebar container class")
    
    return content

def fix_menu_button(content):
    """Perbaiki menu button yang hilang"""
    
    # Cari button dengan class="side" dan pastikan ada onclick
    button_pattern = r'<button[^>]*class="side"[^>]*>'
    
    def fix_button(match):
        button_tag = match.group(0)
        if 'onclick=' not in button_tag:
            # Tambahkan onclick handler
            button_tag = button_tag.replace('>', ' onclick="toggleSidebar()">')
        return button_tag
    
    content = re.sub(button_pattern, fix_button, content)
    print("🔧 Fixed menu button onclick")
    
    return content

def fix_sidebar_toggle(content):
    """Perbaiki sidebar toggle functionality"""
    
    # Pastikan ada onclick="toggleSidebar()" di button
    content = re.sub(
        r'<button([^>]*)class="side"([^>]*)>',
        r'<button\1class="side"\2 onclick="toggleSidebar()">',
        content
    )
    
    # Hapus onclick duplikat
    content = re.sub(r'onclick="toggleSidebar\(\)"[^>]*onclick="toggleSidebar\(\)"', 'onclick="toggleSidebar()"', content)
    
    print("🔧 Fixed sidebar toggle")
    return content

def remove_remaining_amp_classes(content):
    """Hapus sisa class AMP yang masih ada"""
    
    # Hapus class yang mengandung i-amphtml
    content = re.sub(r'\s+class="[^"]*i-amphtml[^"]*"', '', content)
    content = re.sub(r'\s+i-amphtml-[^=]*="[^"]*"', '', content)
    content = re.sub(r'\s+i-amphtml-[^=\s]*', '', content)
    
    # Bersihkan class yang kosong
    content = re.sub(r'\s+class=""', '', content)
    
    print("🧹 Removed remaining AMP classes")
    return content

def fix_button_elements(content):
    """Perbaiki button elements yang hilang"""
    
    # Pastikan semua button memiliki class yang benar
    content = re.sub(
        r'<button([^>]*)class="([^"]*)"([^>]*)>',
        lambda m: f'<button{m.group(1)}class="{m.group(2)} btn"{m.group(3)}>',
        content
    )
    
    # Hapus duplikasi class btn
    content = re.sub(r'class="([^"]*)\s+btn\s+btn([^"]*)"', r'class="\1 btn\2"', content)
    content = re.sub(r'class="btn\s+([^"]*)\s+btn"', r'class="btn \1"', content)
    
    print("🔧 Fixed button elements")
    return content

def enhance_ui_fixes_css():
    """Enhance UI fixes CSS untuk elemen yang diperbaiki"""
    
    additional_css = '''

/* Additional fixes untuk elemen yang hilang */

/* Menu button fixes */
.side.btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #FAF0D7;
}

.side.btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

/* Sidebar container fixes */
.side-bar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: #1a1a1a;
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.3);
}

.side-bar.open {
    right: 0;
}

/* Button general fixes */
.btn {
    display: inline-block;
    padding: 6px 12px;
    touch-action: manipulation;
    cursor: pointer;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    font: 500 18.5px BebasNeue;
    color: #FAF0D7;
    text-shadow: 0 0 3px #000;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Search input fixes */
input[type="search"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
}

input[type="search"]:focus {
    outline: none;
    border-color: #FFF349;
    box-shadow: 0 0 5px rgba(255, 243, 73, 0.3);
}

/* Card fixes */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Image fixes */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .side-bar {
        width: 280px;
        right: -280px;
    }
    
    .flex-container-side {
        padding: 0 10px;
    }
    
    .btn {
        font-size: 16px;
        padding: 4px 8px;
    }
}

@media (max-width: 480px) {
    .side-bar {
        width: 250px;
        right: -250px;
    }
    
    .flex-container-side {
        padding: 0 5px;
    }
}
'''
    
    # Append ke CSS yang ada
    css_file = Path("css/ui-fixes.css")
    with open(css_file, 'a', encoding='utf-8') as f:
        f.write(additional_css)
    
    print("🎨 Enhanced UI fixes CSS")

def enhance_ui_fixes_js():
    """Enhance UI fixes JavaScript"""
    
    additional_js = '''

// Additional JavaScript fixes
document.addEventListener('DOMContentLoaded', function() {
    // Fix any remaining issues
    fixRemainingIssues();
    
    // Initialize enhanced features
    initializeEnhancedFeatures();
});

function fixRemainingIssues() {
    // Fix buttons without proper classes
    const buttons = document.querySelectorAll('button:not(.btn)');
    buttons.forEach(button => {
        if (!button.classList.contains('btn')) {
            button.classList.add('btn');
        }
    });
    
    // Fix images without alt text
    const images = document.querySelectorAll('img:not([alt])');
    images.forEach(img => {
        img.alt = 'Image';
    });
    
    // Fix links without href
    const links = document.querySelectorAll('a:not([href])');
    links.forEach(link => {
        link.href = '#';
        link.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });
    
    console.log('🔧 Fixed remaining issues');
}

function initializeEnhancedFeatures() {
    // Enhanced search functionality
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterContent(searchTerm);
        }, 300));
    }
    
    // Enhanced card interactions
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
    
    console.log('✨ Enhanced features initialized');
}

function filterContent(searchTerm) {
    const cards = document.querySelectorAll('.card');
    let visibleCount = 0;
    
    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        const isVisible = !searchTerm || text.includes(searchTerm);
        
        card.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });
    
    console.log(`🔍 ${visibleCount} items visible`);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Enhanced sidebar functionality
function toggleSidebar() {
    const sidebar = document.querySelector('.side-bar, #sidebar1');
    if (sidebar) {
        sidebar.classList.toggle('open');
        console.log('📱 Sidebar toggled');
    }
}

function closeSidebar() {
    const sidebar = document.querySelector('.side-bar, #sidebar1');
    if (sidebar) {
        sidebar.classList.remove('open');
        console.log('📱 Sidebar closed');
    }
}

// Make functions globally available
window.toggleSidebar = toggleSidebar;
window.closeSidebar = closeSidebar;
'''
    
    # Append ke JS yang ada
    js_file = Path("js/ui-fixes.js")
    with open(js_file, 'a', encoding='utf-8') as f:
        f.write(additional_js)
    
    print("⚡ Enhanced UI fixes JavaScript")

def main():
    """Main function"""
    print("🔧 Fixing missing UI elements...")
    print("=" * 50)
    
    # Perbaiki elemen yang hilang
    content = fix_missing_elements()
    
    # Enhance CSS dan JS
    enhance_ui_fixes_css()
    enhance_ui_fixes_js()
    
    print("\n✅ Missing elements fixes complete!")
    print("\n📁 Files updated:")
    print("   - index_final.html (All elements fixed)")
    print("   - css/ui-fixes.css (Enhanced)")
    print("   - js/ui-fixes.js (Enhanced)")
    
    print("\n🎯 Fixes applied:")
    print("   - ✅ Fixed sidebar container")
    print("   - ✅ Fixed menu button onclick")
    print("   - ✅ Fixed sidebar toggle")
    print("   - ✅ Removed AMP remnants")
    print("   - ✅ Fixed button elements")
    print("   - ✅ Enhanced CSS and JS")
    
    print("\n🌐 Test final version:")
    print("   http://localhost:8080/index_final.html")

if __name__ == "__main__":
    main()
