#!/usr/bin/env python3
"""
Script untuk memperbaiki masalah optimasi index.html
"""

import re
from pathlib import Path

def fix_optimized_html():
    """Perbaiki file HTML yang sudah dioptimasi"""
    
    # Baca file asli untuk referensi
    original_file = Path("index.html")
    with open(original_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # Ekstrak CSS dari file asli
    css_pattern = r'<style amp-custom="">(.*?)</style>'
    css_match = re.search(css_pattern, original_content, re.DOTALL)
    
    if css_match:
        css_content = css_match.group(1).strip()
        
        # Simpan CSS dengan format yang lebih readable
        css_dir = Path("css")
        css_dir.mkdir(exist_ok=True)
        
        with open(css_dir / "styles.css", 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        print(f"✅ CSS berhasil diekstrak: {len(css_content):,} bytes")
    
    # Buat HTML yang bersih dengan CSS terpisah
    create_clean_html(original_content, css_content)

def create_clean_html(original_content, css_content):
    """Buat HTML yang bersih dengan CSS terpisah"""
    
    # Hapus CSS inline dan ganti dengan link eksternal
    css_pattern = r'<style amp-custom="">(.*?)</style>'
    content = re.sub(css_pattern, '<link rel="stylesheet" href="css/styles.css">', original_content, flags=re.DOTALL)
    
    # Hapus duplikasi amp-list dan amp-state
    content = remove_duplicates(content)
    
    # Tambahkan JavaScript untuk functionality
    js_script = '\n    <script src="js/app.js" defer></script>\n</body>'
    content = content.replace('</body>', js_script)
    
    # Simpan HTML yang sudah diperbaiki
    output_file = Path("index_fixed.html")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    original_size = len(original_content)
    fixed_size = len(content)
    reduction = ((original_size - fixed_size) / original_size) * 100
    
    print(f"✅ HTML diperbaiki: {output_file}")
    print(f"📊 Ukuran: {original_size:,} → {fixed_size:,} bytes ({reduction:.1f}% reduction)")

def remove_duplicates(content):
    """Hapus duplikasi amp-list dan amp-state"""
    
    # Cari semua amp-state dengan id="allGames"
    amp_state_pattern = r'<amp-state id="allGames"[^>]*>.*?</amp-state>'
    amp_states = re.findall(amp_state_pattern, content, re.DOTALL)
    
    if len(amp_states) > 1:
        # Hapus semua kecuali yang pertama
        for i in range(1, len(amp_states)):
            content = content.replace(amp_states[i], '', 1)
        print(f"🔧 Removed {len(amp_states) - 1} duplicate amp-state elements")
    
    # Cari duplikasi search input
    search_pattern = r'<input class="form-control" type="search"[^>]*>'
    search_inputs = re.findall(search_pattern, content)
    
    if len(search_inputs) > 1:
        # Hapus duplikasi search input
        for i in range(1, len(search_inputs)):
            content = content.replace(search_inputs[i], '', 1)
        print(f"🔧 Removed {len(search_inputs) - 1} duplicate search inputs")
    
    # Cari duplikasi amp-list untuk games
    game_list_pattern = r'<amp-list[^>]*class="[^"]*game-list[^"]*"[^>]*>.*?</amp-list>'
    game_lists = re.findall(game_list_pattern, content, re.DOTALL)
    
    if len(game_lists) > 1:
        # Hapus duplikasi game list
        for i in range(1, len(game_lists)):
            content = content.replace(game_lists[i], '', 1)
        print(f"🔧 Removed {len(game_lists) - 1} duplicate game lists")
    
    return content

def create_enhanced_js():
    """Buat JavaScript yang enhanced untuk menggantikan AMP functionality"""
    
    js_content = """
// Enhanced JavaScript untuk menggantikan AMP functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Enhanced index.html loaded');
    
    // Initialize all functionality
    initializeSearch();
    initializeSidebar();
    initializeLazyLoading();
    initializeForms();
    initializeGameList();
});

function initializeSearch() {
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterGames(searchTerm);
        }, 300));
        
        console.log('🔍 Search functionality initialized');
    }
}

function filterGames(searchTerm) {
    const gameCards = document.querySelectorAll('.card');
    let visibleCount = 0;
    
    gameCards.forEach(card => {
        const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';
        const gameTitle = card.textContent?.toLowerCase() || '';
        
        const isVisible = searchTerm === '' || 
                         gameName.includes(searchTerm) || 
                         gameTitle.includes(searchTerm);
        
        card.style.display = isVisible ? 'block' : 'none';
        if (isVisible) visibleCount++;
    });
    
    console.log(`🎮 Found ${visibleCount} games matching "${searchTerm}"`);
}

function initializeSidebar() {
    const sidebarToggle = document.querySelector('[on*="sidebar"], .side');
    const sidebar = document.querySelector('amp-sidebar, #sidebar1');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('open');
            sidebar.style.transform = sidebar.classList.contains('open') ? 
                'translateX(0)' : 'translateX(100%)';
        });
        
        console.log('📱 Sidebar functionality initialized');
    }
}

function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px'
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
        
        console.log('🖼️ Lazy loading initialized');
    }
}

function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('📝 Form submitted');
            // Handle form submission here
        });
    });
    
    console.log('📋 Form handling initialized');
}

function initializeGameList() {
    // Simulate AMP list functionality
    loadGames();
}

async function loadGames() {
    try {
        const response = await fetch('/games.json');
        const data = await response.json();
        
        if (data && data.items) {
            console.log(`🎮 Loaded ${data.items.length} games`);
            // Games are already rendered by AMP, just enhance them
            enhanceGameCards();
        }
    } catch (error) {
        console.error('❌ Failed to load games:', error);
    }
}

function enhanceGameCards() {
    const gameCards = document.querySelectorAll('.card');
    gameCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    console.log(`✨ Enhanced ${gameCards.length} game cards`);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance monitoring
window.addEventListener('load', function() {
    const loadTime = performance.now();
    console.log(`⚡ Page loaded in ${Math.round(loadTime)}ms`);
    
    // Check if CSS is loaded
    const cssLink = document.querySelector('link[href*="styles.css"]');
    if (cssLink) {
        console.log('🎨 External CSS loaded successfully');
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('❌ JavaScript error:', e.error);
});
"""
    
    # Simpan JavaScript
    js_dir = Path("js")
    js_dir.mkdir(exist_ok=True)
    
    with open(js_dir / "app.js", 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    print(f"✅ Enhanced JavaScript created: js/app.js")

def main():
    """Main function"""
    print("🔧 Memperbaiki optimasi index.html...")
    print("=" * 50)
    
    # Perbaiki HTML
    fix_optimized_html()
    
    # Buat JavaScript yang enhanced
    create_enhanced_js()
    
    print("\n✅ Perbaikan selesai!")
    print("\n📁 File yang diperbaiki:")
    print("   - index_fixed.html (HTML bersih tanpa duplikasi)")
    print("   - css/styles.css (CSS terpisah, readable)")
    print("   - js/app.js (JavaScript enhanced)")
    
    print("\n🌐 Untuk testing:")
    print("   http://localhost:8080/index_fixed.html")

if __name__ == "__main__":
    main()
