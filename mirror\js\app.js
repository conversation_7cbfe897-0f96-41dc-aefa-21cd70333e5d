/**
 * Game App - Modern Web Application
 * Optimized for maximum performance
 */

class GameApp {
    constructor() {
        this.games = [];
        this.filteredGames = [];
        this.isLoading = false;
        this.searchTimeout = null;
        
        // DOM elements
        this.searchInput = document.getElementById('searchInput');
        this.gamesGrid = document.getElementById('gamesGrid');
        this.loading = document.getElementById('loading');
        this.noResults = document.getElementById('noResults');
        
        // Performance tracking
        this.performanceMetrics = {
            loadStart: performance.now(),
            gamesLoaded: null,
            firstRender: null
        };
        
        this.init();
    }

    async init() {
        try {
            // Show loading state
            this.showLoading();
            
            // Load games data
            await this.loadGames();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initial render
            this.renderGames();
            
            // Setup performance optimizations
            this.setupOptimizations();
            
            // Track performance
            this.trackPerformance();
            
        } catch (error) {
            console.error('App initialization failed:', error);
            this.showError('Failed to initialize app. Please refresh the page.');
        }
    }

    async loadGames() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout
            
            const response = await fetch('games.json', {
                signal: controller.signal,
                cache: 'force-cache' // Use cache if available
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            this.games = await response.json();
            this.filteredGames = [...this.games];
            
            this.performanceMetrics.gamesLoaded = performance.now();
            console.log(`✅ Loaded ${this.games.length} games in ${Math.round(this.performanceMetrics.gamesLoaded - this.performanceMetrics.loadStart)}ms`);
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Request timeout - please check your connection');
            }
            throw error;
        }
    }

    setupEventListeners() {
        // Search with optimized debouncing
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.filterGames(e.target.value);
            }, 300);
        });

        // Clear search on escape
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.target.value = '';
                this.filterGames('');
            }
        });

        // Intersection Observer for lazy loading
        this.setupLazyLoading();
        
        // Visibility API for performance optimization
        this.setupVisibilityOptimization();
    }

    filterGames(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        
        if (!term) {
            this.filteredGames = [...this.games];
        } else {
            // Optimized filtering with early exit
            this.filteredGames = this.games.filter(game => {
                return game.game_name.toLowerCase().includes(term) ||
                       game.provider.toLowerCase().includes(term);
            });
        }
        
        this.renderGames();
    }

    renderGames() {
        this.hideLoading();
        
        if (this.filteredGames.length === 0) {
            this.showNoResults();
            return;
        }

        this.hideNoResults();
        this.gamesGrid.classList.remove('hidden');
        
        // Use DocumentFragment for better performance
        const fragment = document.createDocumentFragment();
        
        // Batch DOM updates
        requestAnimationFrame(() => {
            this.gamesGrid.innerHTML = ''; // Clear existing content
            
            this.filteredGames.forEach((game, index) => {
                const cardElement = this.createGameCard(game, index);
                fragment.appendChild(cardElement);
            });
            
            this.gamesGrid.appendChild(fragment);
            
            // Track first render
            if (!this.performanceMetrics.firstRender) {
                this.performanceMetrics.firstRender = performance.now();
            }
            
            // Trigger staggered animations
            this.animateCards();
        });
    }

    createGameCard(game, index) {
        const card = document.createElement('div');
        card.className = 'game-card';
        card.dataset.gameId = game.id;
        card.dataset.index = index;
        
        // Use template literals for better performance
        card.innerHTML = `
            <div class="game-image">
                <img 
                    data-src="${game.image_url}" 
                    alt="${this.escapeHtml(game.game_name)}"
                    class="lazy-image"
                    loading="lazy">
                <div class="image-placeholder">🎰</div>
                <a href="${game.play_url}" class="play-btn" target="_blank" rel="noopener">
                    ▶ PLAY NOW
                </a>
            </div>
            <div class="game-content">
                <h3 class="game-title">${this.escapeHtml(game.game_name)}</h3>
                
                <div class="rtp-container">
                    <div class="rtp-bar">
                        <div class="rtp-fill ${game.warna}" style="width: 0%" data-width="${game.value}%"></div>
                    </div>
                    <div class="rtp-text">${game.value}% RTP</div>
                </div>
                
                <div class="pola-section">
                    ${this.createPolaContent(game.pola_rtp)}
                </div>
            </div>
        `;
        
        return card;
    }

    createPolaContent(polaRtp) {
        if (!polaRtp.available) {
            return `<div class="pola-unavailable">${polaRtp.message}</div>`;
        }

        return polaRtp.patterns.map(pattern => {
            const icons = pattern.icons.map(icon => 
                `<span class="material-symbols-rounded icon-${icon}">${icon}</span>`
            ).join('');
            
            return `
                <div class="pola-pattern">
                    <span>${this.escapeHtml(pattern.bet)}</span>
                    <div class="pola-icons">${icons}</div>
                    <span>${this.escapeHtml(pattern.type)}</span>
                </div>
            `;
        }).join('');
    }

    animateCards() {
        const cards = this.gamesGrid.querySelectorAll('.game-card');
        
        // Use Intersection Observer for better performance
        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('fade-in');
                        
                        // Animate RTP bar
                        const rtpFill = entry.target.querySelector('.rtp-fill');
                        if (rtpFill) {
                            setTimeout(() => {
                                rtpFill.style.width = rtpFill.dataset.width;
                            }, 200);
                        }
                    }, index * 50);
                    
                    animationObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        cards.forEach(card => animationObserver.observe(card));
    }

    setupLazyLoading() {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const placeholder = img.nextElementSibling;
                    
                    img.onload = () => {
                        img.classList.add('loaded');
                        if (placeholder) placeholder.style.display = 'none';
                    };
                    
                    img.onerror = () => {
                        img.style.display = 'none';
                        if (placeholder) {
                            placeholder.textContent = '❌';
                            placeholder.style.color = '#f44336';
                        }
                    };
                    
                    img.src = img.dataset.src;
                    img.classList.remove('lazy-image');
                    imageObserver.unobserve(img);
                }
            });
        }, { 
            threshold: 0.1,
            rootMargin: '50px' // Load images 50px before they come into view
        });

        // Re-observe images after each render
        const originalRenderGames = this.renderGames.bind(this);
        this.renderGames = function() {
            originalRenderGames();
            setTimeout(() => {
                document.querySelectorAll('.lazy-image').forEach(img => {
                    imageObserver.observe(img);
                });
            }, 100);
        };
    }

    setupOptimizations() {
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Setup memory management
        this.setupMemoryManagement();
        
        // Setup error handling
        this.setupErrorHandling();
    }

    preloadCriticalResources() {
        // Preload first 6 game images
        const criticalGames = this.games.slice(0, 6);
        criticalGames.forEach(game => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = game.image_url;
            document.head.appendChild(link);
        });
    }

    setupMemoryManagement() {
        // Clean up event listeners on page unload
        window.addEventListener('beforeunload', () => {
            clearTimeout(this.searchTimeout);
        });
        
        // Throttle scroll events
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                // Cleanup off-screen images if needed
                this.cleanupOffscreenImages();
            }, 250);
        }, { passive: true });
    }

    cleanupOffscreenImages() {
        const cards = document.querySelectorAll('.game-card');
        const viewportHeight = window.innerHeight;
        
        cards.forEach(card => {
            const rect = card.getBoundingClientRect();
            const isOffscreen = rect.bottom < -viewportHeight || rect.top > viewportHeight * 2;
            
            if (isOffscreen) {
                const img = card.querySelector('img');
                if (img && img.src && img.src !== img.dataset.src) {
                    // Reset to lazy loading state for far off-screen images
                    img.src = '';
                    img.classList.add('lazy-image');
                    img.classList.remove('loaded');
                }
            }
        });
    }

    setupVisibilityOptimization() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Pause animations and timers when tab is hidden
                clearTimeout(this.searchTimeout);
            }
        });
    }

    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            // Could send to analytics service
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            event.preventDefault();
        });
    }

    trackPerformance() {
        // Core Web Vitals tracking
        if ('web-vital' in window) {
            // This would integrate with a real analytics service
            console.log('Performance tracking enabled');
        }
        
        // Custom performance metrics
        const metrics = {
            loadTime: this.performanceMetrics.gamesLoaded - this.performanceMetrics.loadStart,
            renderTime: this.performanceMetrics.firstRender - this.performanceMetrics.gamesLoaded,
            totalGames: this.games.length
        };
        
        console.log('📊 Performance Metrics:', metrics);
    }

    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading() {
        this.loading.classList.remove('hidden');
        this.gamesGrid.classList.add('hidden');
        this.noResults.classList.add('hidden');
    }

    hideLoading() {
        this.loading.classList.add('hidden');
    }

    showNoResults() {
        this.noResults.classList.remove('hidden');
        this.gamesGrid.classList.add('hidden');
    }

    hideNoResults() {
        this.noResults.classList.add('hidden');
    }

    showError(message) {
        this.loading.innerHTML = `
            <div style="color: #f44336; text-align: center;">
                <div style="font-size: 2rem; margin-bottom: 1rem;">❌</div>
                <p>${message}</p>
                <button onclick="location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Refresh Page
                </button>
            </div>
        `;
        this.loading.classList.remove('hidden');
    }
}

// Initialize app
let gameApp;

function initializeApp() {
    try {
        gameApp = new GameApp();
    } catch (error) {
        console.error('Failed to initialize app:', error);
        document.getElementById('loading').innerHTML = `
            <div style="color: #f44336;">
                <p>❌ Failed to load application</p>
                <button onclick="location.reload()">Retry</button>
            </div>
        `;
    }
}

// DOM ready check
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Export for debugging
window.gameApp = gameApp;
