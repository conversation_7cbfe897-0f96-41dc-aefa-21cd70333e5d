
// Enhanced JavaScript untuk menggantikan AMP functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Enhanced index.html loaded');
    
    // Initialize all functionality
    initializeSearch();
    initializeSidebar();
    initializeLazyLoading();
    initializeForms();
    initializeGameList();
});

function initializeSearch() {
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterGames(searchTerm);
        }, 300));
        
        console.log('🔍 Search functionality initialized');
    }
}

function filterGames(searchTerm) {
    const gameCards = document.querySelectorAll('.card');
    let visibleCount = 0;
    
    gameCards.forEach(card => {
        const gameName = card.querySelector('img')?.alt?.toLowerCase() || '';
        const gameTitle = card.textContent?.toLowerCase() || '';
        
        const isVisible = searchTerm === '' || 
                         gameName.includes(searchTerm) || 
                         gameTitle.includes(searchTerm);
        
        card.style.display = isVisible ? 'block' : 'none';
        if (isVisible) visibleCount++;
    });
    
    console.log(`🎮 Found ${visibleCount} games matching "${searchTerm}"`);
}

function initializeSidebar() {
    const sidebarToggle = document.querySelector('[on*="sidebar"], .side');
    const sidebar = document.querySelector('amp-sidebar, #sidebar1');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('open');
            sidebar.style.transform = sidebar.classList.contains('open') ? 
                'translateX(0)' : 'translateX(100%)';
        });
        
        console.log('📱 Sidebar functionality initialized');
    }
}

function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px'
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
        
        console.log('🖼️ Lazy loading initialized');
    }
}

function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('📝 Form submitted');
            // Handle form submission here
        });
    });
    
    console.log('📋 Form handling initialized');
}

function initializeGameList() {
    // Simulate AMP list functionality
    loadGames();
}

async function loadGames() {
    try {
        const response = await fetch('/games.json');
        const data = await response.json();
        
        if (data && data.items) {
            console.log(`🎮 Loaded ${data.items.length} games`);
            // Games are already rendered by AMP, just enhance them
            enhanceGameCards();
        }
    } catch (error) {
        console.error('❌ Failed to load games:', error);
    }
}

function enhanceGameCards() {
    const gameCards = document.querySelectorAll('.card');
    gameCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    console.log(`✨ Enhanced ${gameCards.length} game cards`);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance monitoring
window.addEventListener('load', function() {
    const loadTime = performance.now();
    console.log(`⚡ Page loaded in ${Math.round(loadTime)}ms`);
    
    // Check if CSS is loaded
    const cssLink = document.querySelector('link[href*="styles.css"]');
    if (cssLink) {
        console.log('🎨 External CSS loaded successfully');
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('❌ JavaScript error:', e.error);
});
