/* Critical CSS - Above the fold styles only */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --dark-color: #333;
    --light-color: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
}

/* Header - Critical for above fold */
.header {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--box-shadow);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logo {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Search - Critical for interaction */
.search-container {
    margin: 2rem 0;
    text-align: center;
}

.search-input {
    width: 100%;
    max-width: 400px;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: var(--border-radius);
    background: rgba(255,255,255,0.9);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Loading - Critical for first paint */
.loading {
    text-align: center;
    padding: 3rem;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Games Grid - Critical layout */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 2rem 0;
}

/* Game Card - Critical for first cards */
.game-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
    transform: translateZ(0);
}

.game-image {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
}

.game-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.game-content {
    padding: 1rem;
}

.game-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Utilities */
.hidden { display: none; }

/* Mobile Critical */
@media (max-width: 768px) {
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
        padding: 1rem 0;
    }
    
    .container {
        padding: 0 0.5rem;
    }
}
